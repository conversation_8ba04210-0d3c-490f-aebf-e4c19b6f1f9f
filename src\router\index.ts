import type { RouteRecordRaw } from 'vue-router'

// 导入页面组件
const Home = () => import('@/views/Home.vue')
const Tools = () => import('@/views/Tools.vue')
const ToolDetail = () => import('@/views/ToolDetail.vue')
const Categories = () => import('@/views/Categories.vue')
const Search = () => import('@/views/Search.vue')
const Favorites = () => import('@/views/Favorites.vue')
const History = () => import('@/views/History.vue')
const Settings = () => import('@/views/Settings.vue')
const About = () => import('@/views/About.vue')
const NotFound = () => import('@/views/NotFound.vue')

// 工具页面路由
const toolRoutes: RouteRecordRaw[] = [
  // 编码工具
  {
    path: '/tools/encoding/base64',
    name: 'Base64Encoder',
    component: () => import('@/views/tools/encoding/Base64Encoder.vue'),
    meta: {
      title: 'Base64 编码解码',
      description: '在线 Base64 编码和解码工具',
      keywords: ['base64', '编码', '解码', '转换'],
      category: 'encoding',
      icon: 'mdi:code-braces',
      keepAlive: true
    }
  },
  {
    path: '/tools/encoding/url',
    name: 'URLEncoder',
    component: () => import('@/views/tools/encoding/URLEncoder.vue'),
    meta: {
      title: 'URL 编码解码',
      description: '在线 URL 编码和解码工具',
      keywords: ['url', '编码', '解码', 'uri'],
      category: 'encoding',
      icon: 'mdi:link',
      keepAlive: true
    }
  },
  {
    path: '/tools/encoding/html',
    name: 'HTMLEncoder',
    component: () => import('@/views/tools/encoding/HTMLEncoder.vue'),
    meta: {
      title: 'HTML 编码解码',
      description: '在线 HTML 实体编码和解码工具',
      keywords: ['html', '编码', '解码', '实体'],
      category: 'encoding',
      icon: 'mdi:code-tags',
      keepAlive: true
    }
  },

  // JSON 工具
  {
    path: '/tools/json/formatter',
    name: 'JSONFormatter',
    component: () => import('@/views/tools/json/JSONFormatter.vue'),
    meta: {
      title: 'JSON 格式化',
      description: '在线 JSON 格式化、压缩和验证工具',
      keywords: ['json', '格式化', '压缩', '验证'],
      category: 'json',
      icon: 'mdi:code-json',
      keepAlive: true
    }
  },
  {
    path: '/tools/json/validator',
    name: 'JSONValidator',
    component: () => import('@/views/tools/json/JSONValidator.vue'),
    meta: {
      title: 'JSON 验证器',
      description: '在线 JSON 语法验证和错误检查工具',
      keywords: ['json', '验证', '语法检查'],
      category: 'json',
      icon: 'mdi:check-circle',
      keepAlive: true
    }
  },

  // 文本工具
  {
    path: '/tools/text/case-converter',
    name: 'CaseConverter',
    component: () => import('@/views/tools/text/CaseConverter.vue'),
    meta: {
      title: '大小写转换',
      description: '文本大小写转换工具，支持多种格式',
      keywords: ['大小写', '转换', '文本', 'case'],
      category: 'text',
      icon: 'mdi:format-letter-case',
      keepAlive: true
    }
  },
  {
    path: '/tools/text/word-counter',
    name: 'WordCounter',
    component: () => import('@/views/tools/text/WordCounter.vue'),
    meta: {
      title: '字数统计',
      description: '在线字数、词数、字符数统计工具',
      keywords: ['字数统计', '词数', '字符数'],
      category: 'text',
      icon: 'mdi:counter',
      keepAlive: true
    }
  },

  // 图片工具
  {
    path: '/tools/image/compressor',
    name: 'ImageCompressor',
    component: () => import('@/views/tools/image/ImageCompressor.vue'),
    meta: {
      title: '图片压缩',
      description: '在线图片压缩工具，支持多种格式',
      keywords: ['图片压缩', '图像优化', '文件大小'],
      category: 'image',
      icon: 'mdi:image-size-select-large',
      keepAlive: true
    }
  },
  {
    path: '/tools/image/converter',
    name: 'ImageConverter',
    component: () => import('@/views/tools/image/ImageConverter.vue'),
    meta: {
      title: '图片格式转换',
      description: '在线图片格式转换工具',
      keywords: ['图片转换', '格式转换', '图像'],
      category: 'image',
      icon: 'mdi:image-multiple',
      keepAlive: true
    }
  },

  // 二维码工具
  {
    path: '/tools/qr/generator',
    name: 'QRGenerator',
    component: () => import('@/views/tools/qr/QRGenerator.vue'),
    meta: {
      title: '二维码生成器',
      description: '在线二维码生成工具，支持多种内容类型',
      keywords: ['二维码', '生成器', 'qr code'],
      category: 'qr',
      icon: 'mdi:qrcode',
      keepAlive: true
    }
  },
  {
    path: '/tools/qr/reader',
    name: 'QRReader',
    component: () => import('@/views/tools/qr/QRReader.vue'),
    meta: {
      title: '二维码识别',
      description: '在线二维码识别和解码工具',
      keywords: ['二维码识别', '解码', 'qr reader'],
      category: 'qr',
      icon: 'mdi:qrcode-scan',
      keepAlive: true
    }
  },

  // 哈希工具
  {
    path: '/tools/hash/generator',
    name: 'HashGenerator',
    component: () => import('@/views/tools/hash/HashGenerator.vue'),
    meta: {
      title: '哈希生成器',
      description: '在线哈希值生成工具，支持 MD5、SHA1、SHA256 等',
      keywords: ['哈希', 'hash', 'md5', 'sha1', 'sha256'],
      category: 'hash',
      icon: 'mdi:pound',
      keepAlive: true
    }
  },

  // 时间工具
  {
    path: '/tools/time/timestamp',
    name: 'TimestampConverter',
    component: () => import('@/views/tools/time/TimestampConverter.vue'),
    meta: {
      title: '时间戳转换',
      description: '在线时间戳转换工具',
      keywords: ['时间戳', '时间转换', 'timestamp'],
      category: 'time',
      icon: 'mdi:clock',
      keepAlive: true
    }
  },

  // 颜色工具
  {
    path: '/tools/color/picker',
    name: 'ColorPicker',
    component: () => import('@/views/tools/color/ColorPicker.vue'),
    meta: {
      title: '颜色选择器',
      description: '在线颜色选择和转换工具',
      keywords: ['颜色选择器', '颜色转换', 'color picker'],
      category: 'color',
      icon: 'mdi:palette',
      keepAlive: true
    }
  }
]

// 主要路由配置
export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: 'ToolHub - 现代化在线工具集平台',
      description: '为开发者、设计师和内容创作者提供丰富实用的在线工具',
      keywords: ['在线工具', '开发工具', '设计工具', '实用工具'],
      keepAlive: true
    }
  },
  {
    path: '/tools',
    name: 'Tools',
    component: Tools,
    meta: {
      title: '工具列表',
      description: '浏览所有可用的在线工具',
      keywords: ['工具列表', '在线工具', '工具集合'],
      keepAlive: true
    }
  },
  {
    path: '/tools/:id',
    name: 'ToolDetail',
    component: ToolDetail,
    meta: {
      title: '工具详情',
      description: '查看工具详细信息和使用说明',
      keepAlive: false
    }
  },
  {
    path: '/categories',
    name: 'Categories',
    component: Categories,
    meta: {
      title: '工具分类',
      description: '按分类浏览工具',
      keywords: ['工具分类', '分类浏览'],
      keepAlive: true
    }
  },
  {
    path: '/search',
    name: 'Search',
    component: Search,
    meta: {
      title: '搜索工具',
      description: '搜索您需要的工具',
      keywords: ['搜索', '查找工具'],
      keepAlive: false
    }
  },
  {
    path: '/favorites',
    name: 'Favorites',
    component: Favorites,
    meta: {
      title: '我的收藏',
      description: '管理您收藏的工具',
      keywords: ['收藏', '我的工具'],
      requiresAuth: false,
      keepAlive: true
    }
  },
  {
    path: '/history',
    name: 'History',
    component: History,
    meta: {
      title: '使用历史',
      description: '查看您的工具使用历史',
      keywords: ['历史记录', '使用记录'],
      requiresAuth: false,
      keepAlive: true
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: {
      title: '设置',
      description: '个性化设置和偏好配置',
      keywords: ['设置', '配置', '偏好'],
      keepAlive: true
    }
  },
  {
    path: '/about',
    name: 'About',
    component: About,
    meta: {
      title: '关于我们',
      description: '了解 ToolHub 平台',
      keywords: ['关于', 'ToolHub', '平台介绍'],
      keepAlive: true
    }
  },
  
  // 工具路由
  ...toolRoutes,
  
  // 404 页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '页面未找到',
      description: '您访问的页面不存在',
      hidden: true
    }
  }
]

export default routes
