import { createI18n } from 'vue-i18n'
import type { I18n } from 'vue-i18n'

// 导入语言文件
import zhCN from './zh-CN.json'
import enUS from './en-US.json'

// 支持的语言列表
export const supportedLocales = [
  {
    code: 'zh-CN',
    name: 'Chinese (Simplified)',
    nativeName: '简体中文',
    flag: '🇨🇳',
    rtl: false
  },
  {
    code: 'en-US',
    name: 'English (US)',
    nativeName: 'English',
    flag: '🇺🇸',
    rtl: false
  }
] as const

export type SupportedLocale = typeof supportedLocales[number]['code']

// 默认语言
export const defaultLocale: SupportedLocale = 'zh-CN'

// 语言消息
const messages = {
  'zh-CN': zhCN,
  'en-US': enUS
}

// 检测浏览器语言
function detectBrowserLanguage(): SupportedLocale {
  const browserLang = navigator.language || navigator.languages?.[0] || defaultLocale
  
  // 精确匹配
  if (supportedLocales.some(locale => locale.code === browserLang)) {
    return browserLang as SupportedLocale
  }
  
  // 语言代码匹配（如 zh 匹配 zh-CN）
  const langCode = browserLang.split('-')[0]
  const matchedLocale = supportedLocales.find(locale => 
    locale.code.split('-')[0] === langCode
  )
  
  return matchedLocale?.code || defaultLocale
}

// 从存储中获取语言设置
function getStoredLanguage(): SupportedLocale | null {
  try {
    const stored = localStorage.getItem('toolhub-language')
    if (stored && supportedLocales.some(locale => locale.code === stored)) {
      return stored as SupportedLocale
    }
  } catch (error) {
    console.warn('Failed to get stored language:', error)
  }
  return null
}

// 保存语言设置到存储
function setStoredLanguage(locale: SupportedLocale): void {
  try {
    localStorage.setItem('toolhub-language', locale)
  } catch (error) {
    console.warn('Failed to store language:', error)
  }
}

// 获取初始语言
function getInitialLanguage(): SupportedLocale {
  // 优先使用存储的语言设置
  const storedLang = getStoredLanguage()
  if (storedLang) {
    return storedLang
  }
  
  // 其次使用浏览器语言
  return detectBrowserLanguage()
}

// 创建 i18n 实例
export function setupI18n(): I18n {
  const i18n = createI18n({
    legacy: false,
    locale: getInitialLanguage(),
    fallbackLocale: defaultLocale,
    messages,
    globalInjection: true,
    silentTranslationWarn: true,
    silentFallbackWarn: true,
    formatFallbackMessages: true,
    datetimeFormats: {
      'zh-CN': {
        short: {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        },
        long: {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          weekday: 'long',
          hour: 'numeric',
          minute: 'numeric'
        }
      },
      'en-US': {
        short: {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        },
        long: {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          weekday: 'long',
          hour: 'numeric',
          minute: 'numeric'
        }
      }
    },
    numberFormats: {
      'zh-CN': {
        currency: {
          style: 'currency',
          currency: 'CNY',
          notation: 'standard'
        },
        decimal: {
          style: 'decimal',
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        },
        percent: {
          style: 'percent',
          useGrouping: false
        }
      },
      'en-US': {
        currency: {
          style: 'currency',
          currency: 'USD',
          notation: 'standard'
        },
        decimal: {
          style: 'decimal',
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        },
        percent: {
          style: 'percent',
          useGrouping: false
        }
      }
    }
  })

  return i18n
}

// 切换语言
export function switchLanguage(locale: SupportedLocale, i18n: I18n): void {
  if (!supportedLocales.some(l => l.code === locale)) {
    console.warn(`Unsupported locale: ${locale}`)
    return
  }

  i18n.global.locale.value = locale
  setStoredLanguage(locale)
  
  // 更新 HTML lang 属性
  document.documentElement.lang = locale
  
  // 更新页面方向（RTL/LTR）
  const localeConfig = supportedLocales.find(l => l.code === locale)
  if (localeConfig) {
    document.documentElement.dir = localeConfig.rtl ? 'rtl' : 'ltr'
  }
}

// 获取当前语言配置
export function getCurrentLocaleConfig(locale: string) {
  return supportedLocales.find(l => l.code === locale) || supportedLocales[0]
}

// 格式化相对时间
export function formatRelativeTime(date: Date, locale: string = defaultLocale): string {
  const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' })
  const now = new Date()
  const diffInSeconds = Math.floor((date.getTime() - now.getTime()) / 1000)
  
  const intervals = [
    { unit: 'year' as const, seconds: 31536000 },
    { unit: 'month' as const, seconds: 2592000 },
    { unit: 'day' as const, seconds: 86400 },
    { unit: 'hour' as const, seconds: 3600 },
    { unit: 'minute' as const, seconds: 60 },
    { unit: 'second' as const, seconds: 1 }
  ]
  
  for (const interval of intervals) {
    const count = Math.floor(Math.abs(diffInSeconds) / interval.seconds)
    if (count >= 1) {
      return rtf.format(diffInSeconds < 0 ? -count : count, interval.unit)
    }
  }
  
  return rtf.format(0, 'second')
}

// 导出类型
export type { I18n }
export { messages }
