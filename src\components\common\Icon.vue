<template>
  <i
    :class="[
      'iconify',
      sizeClass,
      colorClass,
      {
        'animate-spin': spin,
        'cursor-pointer': clickable,
        'transition-colors duration-200': !noTransition
      }
    ]"
    :data-icon="name"
    :style="customStyle"
    @click="handleClick"
  ></i>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  name: string
  size?: string | number
  color?: string
  spin?: boolean
  clickable?: boolean
  noTransition?: boolean
}

interface Emits {
  (e: 'click', event: MouseEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  size: '1em',
  color: 'currentColor',
  spin: false,
  clickable: false,
  noTransition: false
})

const emit = defineEmits<Emits>()

// 计算属性
const sizeClass = computed(() => {
  if (typeof props.size === 'string') {
    const sizeMap: Record<string, string> = {
      'xs': 'text-xs',
      'sm': 'text-sm',
      'md': 'text-base',
      'lg': 'text-lg',
      'xl': 'text-xl',
      '2xl': 'text-2xl',
      '3xl': 'text-3xl',
      '4xl': 'text-4xl',
      '5xl': 'text-5xl',
      '6xl': 'text-6xl'
    }
    return sizeMap[props.size] || ''
  }
  return ''
})

const colorClass = computed(() => {
  const colorMap: Record<string, string> = {
    'primary': 'text-primary-600 dark:text-primary-400',
    'secondary': 'text-secondary-600 dark:text-secondary-400',
    'accent': 'text-accent-600 dark:text-accent-400',
    'success': 'text-green-600 dark:text-green-400',
    'warning': 'text-yellow-600 dark:text-yellow-400',
    'error': 'text-red-600 dark:text-red-400',
    'info': 'text-blue-600 dark:text-blue-400',
    'muted': 'text-gray-500 dark:text-gray-400',
    'white': 'text-white',
    'black': 'text-black'
  }
  return colorMap[props.color] || ''
})

const customStyle = computed(() => {
  const style: Record<string, string> = {}
  
  if (typeof props.size === 'number') {
    style.fontSize = `${props.size}px`
  } else if (typeof props.size === 'string' && !sizeClass.value) {
    style.fontSize = props.size
  }
  
  if (props.color && !colorClass.value) {
    style.color = props.color
  }
  
  return style
})

// 方法
const handleClick = (event: MouseEvent) => {
  if (props.clickable) {
    emit('click', event)
  }
}
</script>

<script lang="ts">
// 动态加载 Iconify
let iconifyLoaded = false

const loadIconify = async () => {
  if (iconifyLoaded) return
  
  try {
    // 动态导入 Iconify
    const { loadIcon, addIcon } = await import('@iconify/iconify')
    
    // 预加载常用图标集
    const commonIcons = [
      'mdi:home',
      'mdi:tools',
      'mdi:view-grid',
      'mdi:heart',
      'mdi:history',
      'mdi:cog',
      'mdi:information',
      'mdi:magnify',
      'mdi:menu',
      'mdi:close',
      'mdi:chevron-up',
      'mdi:chevron-down',
      'mdi:chevron-left',
      'mdi:chevron-right',
      'mdi:check',
      'mdi:alert',
      'mdi:alert-circle',
      'mdi:information-outline',
      'mdi:check-circle',
      'mdi:close-circle',
      'mdi:loading',
      'mdi:refresh',
      'mdi:download',
      'mdi:upload',
      'mdi:copy',
      'mdi:share',
      'mdi:link',
      'mdi:external-link',
      'mdi:weather-sunny',
      'mdi:weather-night',
      'mdi:translate',
      'mdi:github',
      'mdi:twitter',
      'mdi:discord',
      'mdi:email',
      'mdi:code-braces',
      'mdi:code-tags',
      'mdi:code-json',
      'mdi:format-letter-case',
      'mdi:counter',
      'mdi:image-size-select-large',
      'mdi:image-multiple',
      'mdi:qrcode',
      'mdi:qrcode-scan',
      'mdi:pound',
      'mdi:clock',
      'mdi:palette'
    ]
    
    // 批量预加载图标
    await Promise.all(
      commonIcons.map(icon => loadIcon(icon).catch(() => {}))
    )
    
    iconifyLoaded = true
  } catch (error) {
    console.warn('Failed to load Iconify:', error)
  }
}

// 在组件挂载时加载 Iconify
if (typeof window !== 'undefined') {
  loadIconify()
}

export default {
  name: 'Icon'
}
</script>

<style scoped>
.iconify {
  display: inline-block;
  vertical-align: middle;
}

.iconify[data-icon]:before {
  content: '';
}

/* 加载状态 */
.iconify:not([data-icon]):before {
  content: '⚬';
  opacity: 0.5;
}

/* 错误状态 */
.iconify[data-icon=""][data-icon]:before {
  content: '⚠';
  color: #ef4444;
}
</style>
