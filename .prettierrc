{"semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "printWidth": 80, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": true, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "useTabs": false, "vueIndentScriptAndStyle": false, "embeddedLanguageFormatting": "auto"}