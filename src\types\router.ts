// 路由相关类型定义

import type { Component } from 'vue'
import type { RouteLocationNormalized, NavigationGuardNext } from 'vue-router'

// 路由元信息类型
export interface RouteMeta {
  // 页面标题
  title?: string
  // 页面描述
  description?: string
  // 页面关键词
  keywords?: string[]
  // 是否需要认证
  requiresAuth?: boolean
  // 需要的权限
  permissions?: string[]
  // 页面角色要求
  roles?: string[]
  // 是否在导航中隐藏
  hidden?: boolean
  // 导航图标
  icon?: string | Component
  // 导航排序
  order?: number
  // 面包屑配置
  breadcrumb?: BreadcrumbConfig
  // 页面过渡动画
  transition?: string
  // 是否缓存页面
  keepAlive?: boolean
  // 页面布局
  layout?: string
  // 是否全屏显示
  fullscreen?: boolean
  // 页面主题
  theme?: 'light' | 'dark' | 'auto'
  // 页面背景
  background?: string
  // 自定义样式类
  bodyClass?: string | string[]
  // SEO 配置
  seo?: SEOConfig
  // 分析配置
  analytics?: AnalyticsConfig
  // 自定义元数据
  custom?: Record<string, any>
}

// 面包屑配置
export interface BreadcrumbConfig {
  // 是否显示面包屑
  show?: boolean
  // 自定义面包屑项
  items?: BreadcrumbItem[]
  // 是否显示首页链接
  showHome?: boolean
  // 分隔符
  separator?: string | Component
}

export interface BreadcrumbItem {
  title: string
  path?: string
  icon?: string | Component
  disabled?: boolean
}

// SEO 配置
export interface SEOConfig {
  // 页面标题模板
  titleTemplate?: string
  // 元描述
  metaDescription?: string
  // 元关键词
  metaKeywords?: string[]
  // Open Graph 配置
  openGraph?: OpenGraphConfig
  // Twitter Card 配置
  twitterCard?: TwitterCardConfig
  // 结构化数据
  structuredData?: Record<string, any>
  // 规范链接
  canonical?: string
  // 语言替代链接
  alternateLanguages?: AlternateLanguage[]
}

export interface OpenGraphConfig {
  title?: string
  description?: string
  image?: string
  url?: string
  type?: string
  siteName?: string
  locale?: string
}

export interface TwitterCardConfig {
  card?: 'summary' | 'summary_large_image' | 'app' | 'player'
  site?: string
  creator?: string
  title?: string
  description?: string
  image?: string
}

export interface AlternateLanguage {
  lang: string
  href: string
}

// 分析配置
export interface AnalyticsConfig {
  // 是否启用页面浏览统计
  pageView?: boolean
  // 自定义事件
  events?: AnalyticsEvent[]
  // 自定义维度
  customDimensions?: Record<string, string>
  // 自定义指标
  customMetrics?: Record<string, number>
}

export interface AnalyticsEvent {
  name: string
  parameters?: Record<string, any>
  trigger?: 'mount' | 'unmount' | 'custom'
}

// 导航守卫类型
export type NavigationGuard = (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) => void | Promise<void>

export type BeforeRouteEnter = (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) => void

export type BeforeRouteUpdate = (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) => void

export type BeforeRouteLeave = (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) => void

// 路由配置类型
export interface RouteConfig {
  path: string
  name?: string
  component?: Component | (() => Promise<Component>)
  components?: Record<string, Component | (() => Promise<Component>)>
  redirect?: string | RouteLocationNormalized
  alias?: string | string[]
  children?: RouteConfig[]
  meta?: RouteMeta
  beforeEnter?: NavigationGuard | NavigationGuard[]
  props?: boolean | Record<string, any> | ((route: RouteLocationNormalized) => Record<string, any>)
  sensitive?: boolean
  strict?: boolean
  end?: boolean
}

// 导航菜单类型
export interface NavigationItem {
  id: string
  title: string
  path?: string
  icon?: string | Component
  badge?: string | number
  children?: NavigationItem[]
  disabled?: boolean
  hidden?: boolean
  external?: boolean
  target?: '_blank' | '_self' | '_parent' | '_top'
  order?: number
  permissions?: string[]
  roles?: string[]
  meta?: Record<string, any>
}

// 路由历史类型
export interface RouteHistory {
  path: string
  name?: string
  title?: string
  timestamp: number
  params?: Record<string, any>
  query?: Record<string, any>
  meta?: RouteMeta
}

// 路由状态类型
export interface RouterState {
  currentRoute: RouteLocationNormalized | null
  history: RouteHistory[]
  isLoading: boolean
  error: string | null
  navigationDirection: 'forward' | 'back' | 'replace' | null
}

// 路由工具类型
export interface RouteUtils {
  // 生成路由路径
  generatePath: (name: string, params?: Record<string, any>) => string
  // 检查路由权限
  checkPermission: (route: RouteLocationNormalized, user?: any) => boolean
  // 获取面包屑
  getBreadcrumbs: (route: RouteLocationNormalized) => BreadcrumbItem[]
  // 获取页面标题
  getPageTitle: (route: RouteLocationNormalized) => string
  // 获取导航菜单
  getNavigationItems: (routes: RouteConfig[]) => NavigationItem[]
  // 过滤路由
  filterRoutes: (routes: RouteConfig[], predicate: (route: RouteConfig) => boolean) => RouteConfig[]
}

// 路由中间件类型
export interface RouteMiddleware {
  name: string
  handler: NavigationGuard
  priority?: number
  global?: boolean
}

// 路由缓存类型
export interface RouteCacheConfig {
  // 缓存键生成函数
  keyGenerator?: (route: RouteLocationNormalized) => string
  // 缓存条件
  condition?: (route: RouteLocationNormalized) => boolean
  // 最大缓存数量
  max?: number
  // 缓存过期时间（毫秒）
  ttl?: number
  // 是否包含查询参数
  includeQuery?: boolean
  // 是否包含路由参数
  includeParams?: boolean
}

// 路由懒加载类型
export interface RouteLazyLoadConfig {
  // 预加载策略
  preload?: 'hover' | 'visible' | 'idle' | 'none'
  // 预加载延迟（毫秒）
  preloadDelay?: number
  // 加载超时时间（毫秒）
  timeout?: number
  // 重试次数
  retries?: number
  // 错误回调
  onError?: (error: Error) => void
  // 加载回调
  onLoad?: (component: Component) => void
}

// 路由动画类型
export interface RouteTransition {
  name: string
  mode?: 'in-out' | 'out-in' | 'default'
  duration?: number | { enter: number; leave: number }
  enterActiveClass?: string
  leaveActiveClass?: string
  enterFromClass?: string
  leaveToClass?: string
  enterToClass?: string
  leaveFromClass?: string
  onBeforeEnter?: (el: Element) => void
  onEnter?: (el: Element, done: () => void) => void
  onAfterEnter?: (el: Element) => void
  onBeforeLeave?: (el: Element) => void
  onLeave?: (el: Element, done: () => void) => void
  onAfterLeave?: (el: Element) => void
}
