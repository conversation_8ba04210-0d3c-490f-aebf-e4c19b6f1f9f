<template>
  <Teleport to="body">
    <div v-if="hasModals">
      <TransitionGroup
        name="modal"
        tag="div"
      >
        <div
          v-for="modal in modals"
          :key="modal.id"
          class="fixed inset-0 z-50 overflow-y-auto"
          :style="{ zIndex: (modal.options?.zIndex || 1000) + index }"
          @click.self="handleMaskClick(modal)"
        >
          <!-- 遮罩层 -->
          <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>

          <!-- 模态框容器 -->
          <div class="flex min-h-full items-center justify-center p-4">
            <div
              :class="[
                'relative bg-white dark:bg-gray-800 rounded-lg shadow-xl transform transition-all',
                'max-h-[90vh] overflow-hidden flex flex-col'
              ]"
              :style="modalStyle(modal)"
              @click.stop
            >
              <!-- 模态框头部 -->
              <div
                v-if="modal.title || modal.options?.closable !== false"
                class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700"
              >
                <h3
                  v-if="modal.title"
                  class="text-lg font-semibold text-gray-900 dark:text-white"
                >
                  {{ modal.title }}
                </h3>
                <button
                  v-if="modal.options?.closable !== false"
                  class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 transition-colors"
                  @click="closeModal(modal.id)"
                >
                  <Icon name="mdi:close" class="h-6 w-6" />
                </button>
              </div>

              <!-- 模态框内容 -->
              <div class="flex-1 overflow-y-auto">
                <component
                  :is="getModalComponent(modal.component)"
                  v-bind="modal.props"
                  @close="closeModal(modal.id)"
                  @confirm="handleConfirm(modal)"
                  @cancel="handleCancel(modal)"
                />
              </div>
            </div>
          </div>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, defineAsyncComponent } from 'vue'
import { useModalStore } from '@/stores/modal'
import type { Modal } from '@/types'
import Icon from './Icon.vue'

// Store
const modalStore = useModalStore()

// 计算属性
const modals = computed(() => modalStore.modals)
const hasModals = computed(() => modals.value.length > 0)

// 动态组件映射
const modalComponents = {
  // 确认对话框
  ConfirmDialog: defineAsyncComponent(() => import('./ConfirmDialog.vue')),
  // 警告对话框
  AlertDialog: defineAsyncComponent(() => import('./AlertDialog.vue')),
  // 输入对话框
  PromptDialog: defineAsyncComponent(() => import('./PromptDialog.vue')),
  // 图片预览
  ImagePreview: defineAsyncComponent(() => import('./ImagePreview.vue')),
  // 设置对话框
  SettingsDialog: defineAsyncComponent(() => import('./SettingsDialog.vue')),
  // 关于对话框
  AboutDialog: defineAsyncComponent(() => import('./AboutDialog.vue')),
  // 反馈对话框
  FeedbackDialog: defineAsyncComponent(() => import('./FeedbackDialog.vue'))
}

// 方法
const getModalComponent = (componentName: string) => {
  return modalComponents[componentName as keyof typeof modalComponents] || 'div'
}

const modalStyle = (modal: Modal) => {
  const style: Record<string, string> = {}
  
  if (modal.options?.width) {
    style.width = typeof modal.options.width === 'number' 
      ? `${modal.options.width}px` 
      : modal.options.width
  } else {
    style.width = '32rem' // 默认宽度
  }
  
  if (modal.options?.height) {
    style.height = typeof modal.options.height === 'number' 
      ? `${modal.options.height}px` 
      : modal.options.height
  }
  
  style.maxWidth = '90vw'
  
  return style
}

const closeModal = (id: string) => {
  modalStore.closeModal(id)
}

const handleMaskClick = (modal: Modal) => {
  if (modal.options?.maskClosable !== false) {
    closeModal(modal.id)
  }
}

const handleConfirm = (modal: Modal) => {
  // 触发确认事件
  if (modal.props?.onConfirm) {
    modal.props.onConfirm()
  }
  closeModal(modal.id)
}

const handleCancel = (modal: Modal) => {
  // 触发取消事件
  if (modal.props?.onCancel) {
    modal.props.onCancel()
  }
  closeModal(modal.id)
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && hasModals.value) {
    const topModal = modals.value[modals.value.length - 1]
    if (topModal && topModal.options?.keyboard !== false) {
      closeModal(topModal.id)
    }
  }
}

// 阻止背景滚动
const preventScroll = (event: Event) => {
  if (hasModals.value) {
    event.preventDefault()
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  document.addEventListener('wheel', preventScroll, { passive: false })
  document.addEventListener('touchmove', preventScroll, { passive: false })
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.removeEventListener('wheel', preventScroll)
  document.removeEventListener('touchmove', preventScroll)
})
</script>

<style scoped>
/* 模态框动画 */
.modal-enter-active {
  transition: all 0.3s ease-out;
}

.modal-leave-active {
  transition: all 0.2s ease-in;
}

.modal-enter-from {
  opacity: 0;
  transform: scale(0.95) translateY(-20px);
}

.modal-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(-20px);
}

.modal-move {
  transition: transform 0.3s ease;
}

/* 遮罩动画 */
.modal-enter-from .fixed.inset-0.bg-black {
  opacity: 0;
}

.modal-leave-to .fixed.inset-0.bg-black {
  opacity: 0;
}
</style>
