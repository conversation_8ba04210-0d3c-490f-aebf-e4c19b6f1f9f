// 组件相关类型定义

import type { Component, VNode } from 'vue'

// 基础组件 Props 类型
export interface BaseComponentProps {
  id?: string
  class?: string | string[] | Record<string, boolean>
  style?: string | Record<string, string>
  testId?: string
}

// 按钮组件类型
export interface ButtonProps extends BaseComponentProps {
  type?: 'button' | 'submit' | 'reset'
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  icon?: string | Component
  iconPosition?: 'left' | 'right'
  block?: boolean
  href?: string
  target?: string
  onClick?: (event: MouseEvent) => void
}

// 输入框组件类型
export interface InputProps extends BaseComponentProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search'
  modelValue?: string | number
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  maxlength?: number
  minlength?: number
  pattern?: string
  autocomplete?: string
  size?: 'sm' | 'md' | 'lg'
  error?: boolean
  errorMessage?: string
  helpText?: string
  prefix?: string | Component
  suffix?: string | Component
  clearable?: boolean
  showPassword?: boolean
  onInput?: (value: string | number) => void
  onChange?: (value: string | number) => void
  onFocus?: (event: FocusEvent) => void
  onBlur?: (event: FocusEvent) => void
  onClear?: () => void
}

// 选择器组件类型
export interface SelectProps extends BaseComponentProps {
  modelValue?: string | number | string[] | number[]
  options: SelectOption[]
  placeholder?: string
  disabled?: boolean
  multiple?: boolean
  searchable?: boolean
  clearable?: boolean
  size?: 'sm' | 'md' | 'lg'
  error?: boolean
  errorMessage?: string
  helpText?: string
  loading?: boolean
  noDataText?: string
  noMatchText?: string
  maxTagCount?: number
  onChange?: (value: any) => void
  onSearch?: (query: string) => void
}

export interface SelectOption {
  label: string
  value: string | number
  disabled?: boolean
  group?: string
  icon?: string | Component
  description?: string
  metadata?: Record<string, any>
}

// 模态框组件类型
export interface ModalProps extends BaseComponentProps {
  modelValue?: boolean
  title?: string
  width?: string | number
  height?: string | number
  closable?: boolean
  maskClosable?: boolean
  keyboard?: boolean
  centered?: boolean
  zIndex?: number
  destroyOnClose?: boolean
  footer?: boolean | VNode
  onOk?: () => void | Promise<void>
  onCancel?: () => void
  onClose?: () => void
}

// 抽屉组件类型
export interface DrawerProps extends BaseComponentProps {
  modelValue?: boolean
  title?: string
  placement?: 'left' | 'right' | 'top' | 'bottom'
  width?: string | number
  height?: string | number
  closable?: boolean
  maskClosable?: boolean
  keyboard?: boolean
  zIndex?: number
  destroyOnClose?: boolean
  onClose?: () => void
}

// 通知组件类型
export interface NotificationProps {
  id?: string
  type?: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  closable?: boolean
  showIcon?: boolean
  actions?: NotificationAction[]
  onClose?: () => void
}

export interface NotificationAction {
  label: string
  action: () => void
  type?: 'primary' | 'secondary'
}

// 表格组件类型
export interface TableProps<T = any> extends BaseComponentProps {
  data: T[]
  columns: TableColumn<T>[]
  loading?: boolean
  pagination?: TablePagination
  selection?: TableSelection<T>
  sorting?: TableSorting
  filtering?: TableFiltering
  size?: 'sm' | 'md' | 'lg'
  bordered?: boolean
  striped?: boolean
  hoverable?: boolean
  sticky?: boolean
  virtual?: boolean
  height?: string | number
  onRowClick?: (row: T, index: number) => void
  onSelectionChange?: (selectedRows: T[]) => void
  onSortChange?: (column: string, order: 'asc' | 'desc' | null) => void
  onFilterChange?: (filters: Record<string, any>) => void
}

export interface TableColumn<T = any> {
  key: string
  title: string
  dataIndex?: keyof T
  width?: string | number
  minWidth?: string | number
  maxWidth?: string | number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  filterable?: boolean
  fixed?: 'left' | 'right'
  ellipsis?: boolean
  render?: (value: any, row: T, index: number) => VNode | string
  customHeaderCell?: (column: TableColumn<T>) => Record<string, any>
  customCell?: (value: any, row: T, index: number) => Record<string, any>
}

export interface TablePagination {
  current: number
  pageSize: number
  total: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: boolean
  pageSizeOptions?: number[]
  onChange?: (page: number, pageSize: number) => void
}

export interface TableSelection<T = any> {
  type: 'checkbox' | 'radio'
  selectedRowKeys: (string | number)[]
  getCheckboxProps?: (row: T) => Record<string, any>
  onSelect?: (row: T, selected: boolean, selectedRows: T[]) => void
  onSelectAll?: (selected: boolean, selectedRows: T[], changeRows: T[]) => void
}

export interface TableSorting {
  column?: string
  order?: 'asc' | 'desc'
  multiple?: boolean
}

export interface TableFiltering {
  filters: Record<string, any>
  filterMode?: 'menu' | 'tree'
}

// 表单组件类型
export interface FormProps extends BaseComponentProps {
  model: Record<string, any>
  rules?: FormRules
  layout?: 'horizontal' | 'vertical' | 'inline'
  labelWidth?: string | number
  labelAlign?: 'left' | 'right'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  readonly?: boolean
  validateOnRuleChange?: boolean
  scrollToError?: boolean
  onSubmit?: (values: Record<string, any>) => void
  onReset?: () => void
  onValuesChange?: (changedValues: Record<string, any>, allValues: Record<string, any>) => void
}

export interface FormItemProps extends BaseComponentProps {
  name?: string
  label?: string
  labelWidth?: string | number
  labelAlign?: 'left' | 'right'
  required?: boolean
  rules?: FormRule[]
  validateStatus?: 'success' | 'warning' | 'error' | 'validating'
  help?: string
  extra?: string
  colon?: boolean
  noStyle?: boolean
}

export interface FormRules {
  [key: string]: FormRule[]
}

export interface FormRule {
  type?: 'string' | 'number' | 'boolean' | 'method' | 'regexp' | 'integer' | 'float' | 'array' | 'object' | 'enum' | 'date' | 'url' | 'hex' | 'email'
  required?: boolean
  pattern?: RegExp
  min?: number
  max?: number
  len?: number
  enum?: any[]
  whitespace?: boolean
  message?: string
  validator?: (rule: FormRule, value: any, callback: (error?: string) => void) => void
  trigger?: 'blur' | 'change' | string[]
}

// 上传组件类型
export interface UploadProps extends BaseComponentProps {
  action?: string
  accept?: string
  multiple?: boolean
  directory?: boolean
  maxCount?: number
  maxSize?: number
  beforeUpload?: (file: File) => boolean | Promise<boolean>
  customRequest?: (options: UploadRequestOption) => void
  onChange?: (info: UploadChangeParam) => void
  onPreview?: (file: UploadFile) => void
  onRemove?: (file: UploadFile) => boolean | Promise<boolean>
  onProgress?: (percent: number, file: UploadFile) => void
  onSuccess?: (response: any, file: UploadFile) => void
  onError?: (error: Error, file: UploadFile) => void
}

export interface UploadFile {
  uid: string
  name: string
  status: 'uploading' | 'done' | 'error' | 'removed'
  url?: string
  thumbUrl?: string
  size?: number
  type?: string
  percent?: number
  originFileObj?: File
  response?: any
  error?: any
}

export interface UploadRequestOption {
  action: string
  filename: string
  file: File
  data?: Record<string, any>
  headers?: Record<string, string>
  withCredentials?: boolean
  onProgress?: (percent: number) => void
  onSuccess?: (response: any) => void
  onError?: (error: Error) => void
}

export interface UploadChangeParam {
  file: UploadFile
  fileList: UploadFile[]
  event?: ProgressEvent
}

// 树形组件类型
export interface TreeProps<T = any> extends BaseComponentProps {
  data: TreeNode<T>[]
  checkable?: boolean
  selectable?: boolean
  multiple?: boolean
  expandedKeys?: (string | number)[]
  selectedKeys?: (string | number)[]
  checkedKeys?: (string | number)[]
  loadData?: (node: TreeNode<T>) => Promise<void>
  filterTreeNode?: (node: TreeNode<T>) => boolean
  onExpand?: (expandedKeys: (string | number)[], info: TreeExpandInfo<T>) => void
  onSelect?: (selectedKeys: (string | number)[], info: TreeSelectInfo<T>) => void
  onCheck?: (checkedKeys: (string | number)[], info: TreeCheckInfo<T>) => void
}

export interface TreeNode<T = any> {
  key: string | number
  title: string
  children?: TreeNode<T>[]
  disabled?: boolean
  disableCheckbox?: boolean
  selectable?: boolean
  checkable?: boolean
  isLeaf?: boolean
  icon?: string | Component
  data?: T
}

export interface TreeExpandInfo<T = any> {
  node: TreeNode<T>
  expanded: boolean
  nativeEvent: MouseEvent
}

export interface TreeSelectInfo<T = any> {
  node: TreeNode<T>
  selected: boolean
  selectedNodes: TreeNode<T>[]
  nativeEvent: MouseEvent
}

export interface TreeCheckInfo<T = any> {
  node: TreeNode<T>
  checked: boolean
  checkedNodes: TreeNode<T>[]
  halfCheckedKeys: (string | number)[]
  nativeEvent: MouseEvent
}
