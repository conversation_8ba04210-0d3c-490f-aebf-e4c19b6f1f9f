import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import { setupI18n } from './locales'
import { routes } from './router'

// 导入样式
import './assets/styles/main.css'

async function bootstrap() {
  const app = createApp(App)

  // 创建 Pinia 实例
  const pinia = createPinia()
  app.use(pinia)

  // 创建路由实例
  const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes,
    scrollBehavior(to, from, savedPosition) {
      if (savedPosition) {
        return savedPosition
      } else {
        return { top: 0 }
      }
    }
  })

  app.use(router)

  // 设置国际化
  const i18n = await setupI18n()
  app.use(i18n)

  // 全局错误处理
  app.config.errorHandler = (err, vm, info) => {
    console.error('Global error:', err, info)
    // 这里可以集成错误监控服务，如 Sentry
  }

  // 全局警告处理
  app.config.warnHandler = (msg, vm, trace) => {
    console.warn('Global warning:', msg, trace)
  }

  // 挂载应用
  app.mount('#app')
}

// 启动应用
bootstrap().catch(console.error)
