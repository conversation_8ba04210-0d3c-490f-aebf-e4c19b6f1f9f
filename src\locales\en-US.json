{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "confirm": "Confirm", "cancel": "Cancel", "ok": "OK", "yes": "Yes", "no": "No", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "remove": "Remove", "clear": "Clear", "reset": "Reset", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "close": "Close", "open": "Open", "download": "Download", "upload": "Upload", "import": "Import", "export": "Export", "share": "Share", "favorite": "Favorite", "unfavorite": "Unfavorite", "more": "More", "less": "Less", "expand": "Expand", "collapse": "Collapse", "next": "Next", "previous": "Previous", "back": "Back", "forward": "Forward", "home": "Home", "settings": "Settings", "help": "Help", "about": "About", "contact": "Contact Us", "privacy": "Privacy Policy", "terms": "Terms of Service", "version": "Version", "language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "auto": "Auto", "online": "Online", "offline": "Offline", "connected": "Connected", "disconnected": "Disconnected", "empty": "No data", "noResults": "No results found", "tryAgain": "Try Again", "goBack": "Go Back", "comingSoon": "Coming Soon", "allRightsReserved": "All rights reserved.", "quickAccess": "Quick Access", "backToTop": "Back to Top"}, "nav": {"home": "Home", "tools": "Tools", "categories": "Categories", "favorites": "Favorites", "history": "History", "settings": "Settings", "about": "About"}, "home": {"title": "ToolHub - Modern Online Tools Platform", "subtitle": "Providing rich and practical online tools for developers, designers, and content creators", "searchPlaceholder": "Search tools...", "popularTools": "Popular Tools", "recentTools": "Recently Used", "allCategories": "All Categories", "featuredTools": "Featured Tools", "newTools": "New Tools", "quickAccess": "Quick Access", "getStarted": "Get Started", "learnMore": "Learn More"}, "tools": {"title": "Tools", "subtitle": "Browse all available online tools", "searchPlaceholder": "Search tool name or description...", "filterByCategory": "Filter by Category", "filterByDifficulty": "Filter by <PERSON><PERSON><PERSON><PERSON><PERSON>", "sortBy": "Sort By", "sortByName": "By Name", "sortByPopularity": "By Popularity", "sortByDate": "By Date", "viewMode": "View Mode", "gridView": "Grid View", "listView": "List View", "showFavorites": "Show Favorites Only", "showNew": "Show New Tools Only", "difficulty": {"easy": "Easy", "medium": "Medium", "hard": "Hard"}, "estimatedTime": "Estimated Time", "minutes": "minutes", "features": "Features", "limitations": "Limitations", "relatedTools": "Related Tools", "useTool": "Use Tool", "viewDetails": "View Details", "addToFavorites": "Add to Favorites", "removeFromFavorites": "Remove from Favorites", "copyLink": "Copy Link", "shareOnSocial": "Share on Social Media"}, "categories": {"title": "Tool Categories", "subtitle": "Browse tools by category", "encoding": "Encoding Tools", "json": "JSON Tools", "text": "Text Tools", "image": "Image Tools", "qr": "QR Code Tools", "hash": "<PERSON><PERSON>", "time": "Time Tools", "color": "Color Tools", "pdf": "PDF Tools", "audio": "Audio Tools", "video": "Video Tools", "network": "Network Tools", "security": "Security Tools", "development": "Development Tools", "design": "Design Tools", "productivity": "Productivity Tools", "utility": "Utility Tools"}, "search": {"title": "Search Tools", "placeholder": "Enter keywords to search tools...", "results": "Search Results", "noResults": "No related tools found", "suggestions": "Search Suggestions", "filters": "Filters", "clearFilters": "Clear Filters", "searchHistory": "Search History", "clearHistory": "Clear History", "popularSearches": "Popular Searches"}, "favorites": {"title": "My Favorites", "subtitle": "Manage your favorite tools", "empty": "You haven't favorited any tools yet", "addSome": "Go discover some useful tools", "removeConfirm": "Are you sure you want to remove this tool from favorites?", "clearAll": "Clear All", "clearAllConfirm": "Are you sure you want to clear all favorites? This action cannot be undone.", "exportFavorites": "Export Favorites", "importFavorites": "Import Favorites"}, "history": {"title": "Usage History", "subtitle": "View your tool usage history", "empty": "No usage history", "clearHistory": "Clear History", "clearHistoryConfirm": "Are you sure you want to clear all usage history? This action cannot be undone.", "today": "Today", "yesterday": "Yesterday", "thisWeek": "This Week", "lastWeek": "Last Week", "thisMonth": "This Month", "lastMonth": "Last Month", "older": "Older", "usedAt": "Used At", "removeFromHistory": "Remove from History"}, "settings": {"title": "Settings", "subtitle": "Personalization settings and preferences", "general": "General Settings", "appearance": "Appearance Settings", "language": "Language Settings", "privacy": "Privacy Settings", "data": "Data Management", "about": "About App", "theme": "Theme Mode", "themeLight": "Light Mode", "themeDark": "Dark Mode", "themeAuto": "Follow System", "languageSelect": "Select Language", "fontSize": "Font Size", "fontSizeSmall": "Small", "fontSizeMedium": "Medium", "fontSizeLarge": "Large", "animations": "Animations", "enableAnimations": "Enable Animations", "reducedMotion": "Reduce Motion", "notifications": "Notification Settings", "enableNotifications": "Enable Notifications", "soundEffects": "Sound Effects", "enableSounds": "Enable Sounds", "dataCollection": "Data Collection", "allowAnalytics": "Allow Analytics", "allowCookies": "Allow Cookies", "exportData": "Export Data", "importData": "Import Data", "clearData": "Clear Data", "clearDataConfirm": "Are you sure you want to clear all local data? This action cannot be undone.", "resetSettings": "Reset Settings", "resetSettingsConfirm": "Are you sure you want to reset all settings to default values?"}, "about": {"title": "About ToolHub", "subtitle": "Learn about our platform", "description": "ToolHub is a modern online tools platform dedicated to providing rich and practical online tools for developers, designers, and content creators. Our goal is to make complex tasks simple and improve your work efficiency.", "features": "Platform Features", "feature1": "Rich Tool Collection", "feature1Desc": "Covering multiple fields including encoding, text processing, image processing, format conversion, etc.", "feature2": "Modern Interface", "feature2Desc": "Clean and beautiful user interface with dark mode and multi-language support", "feature3": "Privacy Protection", "feature3Desc": "All processing is done locally to protect your data privacy", "feature4": "Open Source & Free", "feature4Desc": "Completely free to use, open source code, continuous updates", "version": "Current Version", "lastUpdate": "Last Update", "developer": "Development Team", "contact": "Contact Us", "github": "GitHub Repository", "feedback": "<PERSON><PERSON><PERSON>", "changelog": "Changelog", "license": "Open Source License"}, "error": {"404": "Page Not Found", "404Desc": "Sorry, the page you are looking for does not exist.", "500": "Server Error", "500Desc": "The server encountered some problems, please try again later.", "networkError": "Network Error", "networkErrorDesc": "Please check your network connection.", "unknownError": "Unknown Error", "unknownErrorDesc": "An unknown error occurred, please refresh the page and try again.", "goHome": "Go Home", "retry": "Retry", "reportIssue": "Report Issue"}, "notification": {"copied": "Copied to clipboard", "saved": "Saved successfully", "deleted": "Deleted successfully", "updated": "Updated successfully", "uploaded": "Uploaded successfully", "downloaded": "Downloaded successfully", "shared": "Shared successfully", "favoriteAdded": "Added to favorites", "favoriteRemoved": "Removed from favorites", "settingsSaved": "Setting<PERSON> saved", "dataCleared": "Data cleared", "historyCleared": "History cleared", "operationFailed": "Operation failed", "invalidInput": "Invalid input", "fileTooLarge": "File too large", "unsupportedFormat": "Unsupported format", "processingFailed": "Processing failed"}}