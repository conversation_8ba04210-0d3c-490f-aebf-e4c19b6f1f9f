<template>
  <div class="min-h-screen bg-gradient-to-br from-primary-50 to-accent-50 dark:from-gray-900 dark:to-gray-800">
    <!-- 英雄区域 -->
    <section class="relative overflow-hidden">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
        <div class="text-center">
          <!-- 主标题 -->
          <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            <span class="gradient-text">ToolHub</span>
          </h1>
          
          <!-- 副标题 -->
          <p class="text-xl sm:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            {{ $t('home.subtitle') }}
          </p>

          <!-- 搜索框 -->
          <div class="max-w-2xl mx-auto mb-12">
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <Icon name="mdi:magnify" class="h-6 w-6 text-gray-400" />
              </div>
              <input
                v-model="searchQuery"
                type="text"
                :placeholder="$t('home.searchPlaceholder')"
                class="block w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl leading-6 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 shadow-lg"
                @keyup.enter="handleSearch"
              />
              <div class="absolute inset-y-0 right-0 pr-4 flex items-center">
                <button
                  class="p-2 text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                  @click="handleSearch"
                >
                  <Icon name="mdi:arrow-right" class="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>

          <!-- 快速访问按钮 -->
          <div class="flex flex-wrap justify-center gap-4 mb-16">
            <RouterLink
              to="/tools"
              class="btn btn-primary btn-lg"
            >
              <Icon name="mdi:tools" class="h-5 w-5 mr-2" />
              {{ $t('home.getStarted') }}
            </RouterLink>
            <RouterLink
              to="/categories"
              class="btn btn-outline btn-lg"
            >
              <Icon name="mdi:view-grid" class="h-5 w-5 mr-2" />
              {{ $t('home.allCategories') }}
            </RouterLink>
          </div>
        </div>
      </div>

      <!-- 背景装饰 -->
      <div class="absolute inset-0 -z-10">
        <div class="absolute top-1/4 left-1/4 w-72 h-72 bg-primary-200 dark:bg-primary-800 rounded-full mix-blend-multiply dark:mix-blend-screen filter blur-xl opacity-70 animate-blob"></div>
        <div class="absolute top-1/3 right-1/4 w-72 h-72 bg-accent-200 dark:bg-accent-800 rounded-full mix-blend-multiply dark:mix-blend-screen filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div class="absolute bottom-1/4 left-1/3 w-72 h-72 bg-secondary-200 dark:bg-secondary-800 rounded-full mix-blend-multiply dark:mix-blend-screen filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>
    </section>

    <!-- 热门工具 -->
    <section class="section-padding bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm">
      <div class="container">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            {{ $t('home.popularTools') }}
          </h2>
          <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            最受欢迎的在线工具，帮助您快速完成各种任务
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <div
            v-for="tool in popularTools"
            :key="tool.id"
            class="card card-hover p-6 group cursor-pointer"
            @click="navigateToTool(tool)"
          >
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                <Icon :name="tool.icon" class="h-6 w-6 text-white" />
              </div>
              <div class="flex-1">
                <h3 class="font-semibold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                  {{ tool.name }}
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ tool.category }}
                </p>
              </div>
            </div>
            <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
              {{ tool.description }}
            </p>
            <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
              <span>{{ tool.estimatedTime }} 分钟</span>
              <span class="flex items-center">
                <Icon name="mdi:heart" class="h-4 w-4 mr-1" />
                {{ tool.favorites }}
              </span>
            </div>
          </div>
        </div>

        <div class="text-center mt-12">
          <RouterLink
            to="/tools"
            class="btn btn-outline"
          >
            查看所有工具
            <Icon name="mdi:arrow-right" class="h-4 w-4 ml-2" />
          </RouterLink>
        </div>
      </div>
    </section>

    <!-- 工具分类 -->
    <section class="section-padding">
      <div class="container">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            {{ $t('home.allCategories') }}
          </h2>
          <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            按分类浏览工具，快速找到您需要的功能
          </p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          <RouterLink
            v-for="category in categories"
            :key="category.id"
            :to="`/tools?category=${category.id}`"
            class="card card-hover p-6 text-center group"
          >
            <div class="w-16 h-16 mx-auto mb-4 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform"
                 :style="{ backgroundColor: category.color + '20' }">
              <Icon :name="category.icon" class="h-8 w-8" :style="{ color: category.color }" />
            </div>
            <h3 class="font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
              {{ $t(`categories.${category.id}`) }}
            </h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">
              {{ category.description }}
            </p>
            <span class="text-xs text-gray-400">
              {{ category.toolCount }} 个工具
            </span>
          </RouterLink>
        </div>
      </div>
    </section>

    <!-- 特性介绍 -->
    <section class="section-padding bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm">
      <div class="container">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            为什么选择 ToolHub？
          </h2>
          <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            我们致力于提供最好的在线工具体验
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div
            v-for="feature in features"
            :key="feature.title"
            class="text-center"
          >
            <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Icon :name="feature.icon" class="h-8 w-8 text-white" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              {{ feature.title }}
            </h3>
            <p class="text-gray-600 dark:text-gray-300">
              {{ feature.description }}
            </p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import Icon from '@/components/common/Icon.vue'

// 响应式数据
const searchQuery = ref('')

// 组合式 API
const router = useRouter()
const { t } = useI18n()

// 热门工具数据
const popularTools = ref([
  {
    id: 'base64-encoder',
    name: 'Base64 编码解码',
    description: '快速进行 Base64 编码和解码转换',
    category: '编码工具',
    icon: 'mdi:code-braces',
    estimatedTime: 1,
    favorites: 1234,
    path: '/tools/encoding/base64'
  },
  {
    id: 'json-formatter',
    name: 'JSON 格式化',
    description: '格式化、压缩和验证 JSON 数据',
    category: 'JSON 工具',
    icon: 'mdi:code-json',
    estimatedTime: 2,
    favorites: 987,
    path: '/tools/json/formatter'
  },
  {
    id: 'qr-generator',
    name: '二维码生成器',
    description: '生成各种类型的二维码',
    category: '二维码工具',
    icon: 'mdi:qrcode',
    estimatedTime: 1,
    favorites: 756,
    path: '/tools/qr/generator'
  },
  {
    id: 'image-compressor',
    name: '图片压缩',
    description: '在线压缩图片，减小文件大小',
    category: '图片工具',
    icon: 'mdi:image-size-select-large',
    estimatedTime: 3,
    favorites: 654,
    path: '/tools/image/compressor'
  },
  {
    id: 'hash-generator',
    name: '哈希生成器',
    description: '生成 MD5、SHA1、SHA256 等哈希值',
    category: '哈希工具',
    icon: 'mdi:pound',
    estimatedTime: 1,
    favorites: 543,
    path: '/tools/hash/generator'
  },
  {
    id: 'color-picker',
    name: '颜色选择器',
    description: '选择和转换各种颜色格式',
    category: '颜色工具',
    icon: 'mdi:palette',
    estimatedTime: 2,
    favorites: 432,
    path: '/tools/color/picker'
  },
  {
    id: 'url-encoder',
    name: 'URL 编码解码',
    description: '对 URL 进行编码和解码处理',
    category: '编码工具',
    icon: 'mdi:link',
    estimatedTime: 1,
    favorites: 321,
    path: '/tools/encoding/url'
  },
  {
    id: 'timestamp-converter',
    name: '时间戳转换',
    description: '时间戳与日期时间相互转换',
    category: '时间工具',
    icon: 'mdi:clock',
    estimatedTime: 1,
    favorites: 210,
    path: '/tools/time/timestamp'
  }
])

// 分类数据
const categories = ref([
  {
    id: 'encoding',
    name: '编码工具',
    description: '各种编码解码工具',
    icon: 'mdi:code-braces',
    color: '#3b82f6',
    toolCount: 5
  },
  {
    id: 'json',
    name: 'JSON 工具',
    description: 'JSON 处理工具',
    icon: 'mdi:code-json',
    color: '#10b981',
    toolCount: 3
  },
  {
    id: 'text',
    name: '文本工具',
    description: '文本处理和转换',
    icon: 'mdi:format-letter-case',
    color: '#f59e0b',
    toolCount: 8
  },
  {
    id: 'image',
    name: '图片工具',
    description: '图片处理和转换',
    icon: 'mdi:image-multiple',
    color: '#ef4444',
    toolCount: 6
  },
  {
    id: 'qr',
    name: '二维码工具',
    description: '二维码生成和识别',
    icon: 'mdi:qrcode',
    color: '#8b5cf6',
    toolCount: 2
  },
  {
    id: 'hash',
    name: '哈希工具',
    description: '哈希值生成和验证',
    icon: 'mdi:pound',
    color: '#06b6d4',
    toolCount: 4
  },
  {
    id: 'time',
    name: '时间工具',
    description: '时间处理和转换',
    icon: 'mdi:clock',
    color: '#84cc16',
    toolCount: 3
  },
  {
    id: 'color',
    name: '颜色工具',
    description: '颜色选择和转换',
    icon: 'mdi:palette',
    color: '#ec4899',
    toolCount: 4
  }
])

// 特性数据
const features = ref([
  {
    title: '完全免费',
    description: '所有工具完全免费使用，无需注册或付费',
    icon: 'mdi:gift'
  },
  {
    title: '隐私保护',
    description: '所有处理都在本地进行，保护您的数据隐私',
    icon: 'mdi:shield-check'
  },
  {
    title: '现代界面',
    description: '简洁美观的用户界面，支持深色模式',
    icon: 'mdi:palette'
  },
  {
    title: '响应式设计',
    description: '完美适配各种设备，随时随地使用',
    icon: 'mdi:devices'
  },
  {
    title: '快速高效',
    description: '优化的算法和代码，提供快速的处理体验',
    icon: 'mdi:lightning-bolt'
  },
  {
    title: '持续更新',
    description: '定期添加新工具和功能，不断改进用户体验',
    icon: 'mdi:update'
  }
])

// 方法
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({
      name: 'Search',
      query: { q: searchQuery.value.trim() }
    })
  }
}

const navigateToTool = (tool: any) => {
  router.push(tool.path)
}

// 生命周期
onMounted(() => {
  // 这里可以加载动态数据
})
</script>

<style scoped>
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}
</style>
