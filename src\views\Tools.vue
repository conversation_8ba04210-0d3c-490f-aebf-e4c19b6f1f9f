<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          {{ $t('tools.title') }}
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          {{ $t('tools.subtitle') }}
        </p>
      </div>

      <!-- 搜索和筛选 -->
      <div class="mb-8 space-y-4">
        <!-- 搜索框 -->
        <div class="relative max-w-md">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Icon name="mdi:magnify" class="h-5 w-5 text-gray-400" />
          </div>
          <input
            v-model="searchQuery"
            type="text"
            :placeholder="$t('tools.searchPlaceholder')"
            class="input pl-10"
          />
        </div>

        <!-- 筛选器 -->
        <div class="flex flex-wrap gap-4">
          <!-- 分类筛选 -->
          <select
            v-model="selectedCategory"
            class="input w-auto min-w-[150px]"
          >
            <option value="">{{ $t('tools.filterByCategory') }}</option>
            <option
              v-for="category in categories"
              :key="category.id"
              :value="category.id"
            >
              {{ $t(`categories.${category.id}`) }}
            </option>
          </select>

          <!-- 难度筛选 -->
          <select
            v-model="selectedDifficulty"
            class="input w-auto min-w-[120px]"
          >
            <option value="">{{ $t('tools.filterByDifficulty') }}</option>
            <option value="easy">{{ $t('tools.difficulty.easy') }}</option>
            <option value="medium">{{ $t('tools.difficulty.medium') }}</option>
            <option value="hard">{{ $t('tools.difficulty.hard') }}</option>
          </select>

          <!-- 排序 -->
          <select
            v-model="sortBy"
            class="input w-auto min-w-[120px]"
          >
            <option value="name">{{ $t('tools.sortByName') }}</option>
            <option value="popularity">{{ $t('tools.sortByPopularity') }}</option>
            <option value="date">{{ $t('tools.sortByDate') }}</option>
          </select>

          <!-- 清除筛选 -->
          <button
            v-if="hasFilters"
            class="btn btn-ghost"
            @click="clearFilters"
          >
            {{ $t('common.clear') }}
          </button>
        </div>
      </div>

      <!-- 工具网格 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <div
          v-for="tool in filteredTools"
          :key="tool.id"
          class="card card-hover p-6 group cursor-pointer"
          @click="navigateToTool(tool)"
        >
          <!-- 工具图标和标题 -->
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
              <Icon :name="tool.icon" class="h-6 w-6 text-white" />
            </div>
            <div class="flex-1">
              <h3 class="font-semibold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                {{ tool.name }}
              </h3>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                {{ $t(`categories.${tool.category}`) }}
              </span>
            </div>
          </div>

          <!-- 工具描述 -->
          <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
            {{ tool.description }}
          </p>

          <!-- 工具标签 -->
          <div class="flex flex-wrap gap-1 mb-4">
            <span
              v-for="tag in tool.tags.slice(0, 3)"
              :key="tag"
              class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200"
            >
              {{ tag }}
            </span>
            <span
              v-if="tool.tags.length > 3"
              class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400"
            >
              +{{ tool.tags.length - 3 }}
            </span>
          </div>

          <!-- 工具信息 -->
          <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <div class="flex items-center space-x-3">
              <span class="flex items-center">
                <Icon name="mdi:clock" class="h-4 w-4 mr-1" />
                {{ tool.estimatedTime }}{{ $t('tools.minutes') }}
              </span>
              <span
                :class="[
                  'px-2 py-1 rounded-full text-xs font-medium',
                  tool.difficulty === 'easy' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                  tool.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                  'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                ]"
              >
                {{ $t(`tools.difficulty.${tool.difficulty}`) }}
              </span>
            </div>
            <div class="flex items-center space-x-2">
              <button
                class="text-gray-400 hover:text-red-500 transition-colors"
                @click.stop="toggleFavorite(tool)"
              >
                <Icon
                  :name="tool.isFavorite ? 'mdi:heart' : 'mdi:heart-outline'"
                  :class="tool.isFavorite ? 'text-red-500' : ''"
                  class="h-4 w-4"
                />
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div
        v-if="filteredTools.length === 0"
        class="text-center py-12"
      >
        <Icon name="mdi:tools" class="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {{ $t('common.noResults') }}
        </h3>
        <p class="text-gray-500 dark:text-gray-400 mb-4">
          尝试调整搜索条件或筛选器
        </p>
        <button
          class="btn btn-primary"
          @click="clearFilters"
        >
          {{ $t('common.clear') }}{{ $t('search.filters') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import Icon from '@/components/common/Icon.vue'

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedDifficulty = ref('')
const sortBy = ref('name')

// 组合式 API
const router = useRouter()
const { t } = useI18n()

// 模拟工具数据
const tools = ref([
  {
    id: 'base64-encoder',
    name: 'Base64 编码解码',
    description: '快速进行 Base64 编码和解码转换',
    category: 'encoding',
    icon: 'mdi:code-braces',
    tags: ['base64', '编码', '解码'],
    difficulty: 'easy' as const,
    estimatedTime: 1,
    isFavorite: false,
    isNew: false,
    path: '/tools/encoding/base64'
  },
  {
    id: 'json-formatter',
    name: 'JSON 格式化',
    description: '格式化、压缩和验证 JSON 数据',
    category: 'json',
    icon: 'mdi:code-json',
    tags: ['json', '格式化', '验证'],
    difficulty: 'easy' as const,
    estimatedTime: 2,
    isFavorite: true,
    isNew: false,
    path: '/tools/json/formatter'
  },
  {
    id: 'qr-generator',
    name: '二维码生成器',
    description: '生成各种类型的二维码',
    category: 'qr',
    icon: 'mdi:qrcode',
    tags: ['二维码', '生成器', 'qr'],
    difficulty: 'easy' as const,
    estimatedTime: 1,
    isFavorite: false,
    isNew: true,
    path: '/tools/qr/generator'
  },
  {
    id: 'image-compressor',
    name: '图片压缩',
    description: '在线压缩图片，减小文件大小',
    category: 'image',
    icon: 'mdi:image-size-select-large',
    tags: ['图片', '压缩', '优化'],
    difficulty: 'medium' as const,
    estimatedTime: 3,
    isFavorite: false,
    isNew: false,
    path: '/tools/image/compressor'
  },
  {
    id: 'hash-generator',
    name: '哈希生成器',
    description: '生成 MD5、SHA1、SHA256 等哈希值',
    category: 'hash',
    icon: 'mdi:pound',
    tags: ['哈希', 'md5', 'sha256'],
    difficulty: 'easy' as const,
    estimatedTime: 1,
    isFavorite: false,
    isNew: false,
    path: '/tools/hash/generator'
  },
  {
    id: 'color-picker',
    name: '颜色选择器',
    description: '选择和转换各种颜色格式',
    category: 'color',
    icon: 'mdi:palette',
    tags: ['颜色', '选择器', '转换'],
    difficulty: 'medium' as const,
    estimatedTime: 2,
    isFavorite: true,
    isNew: false,
    path: '/tools/color/picker'
  }
])

// 分类数据
const categories = ref([
  { id: 'encoding', name: '编码工具' },
  { id: 'json', name: 'JSON 工具' },
  { id: 'text', name: '文本工具' },
  { id: 'image', name: '图片工具' },
  { id: 'qr', name: '二维码工具' },
  { id: 'hash', name: '哈希工具' },
  { id: 'time', name: '时间工具' },
  { id: 'color', name: '颜色工具' }
])

// 计算属性
const hasFilters = computed(() => {
  return searchQuery.value || selectedCategory.value || selectedDifficulty.value
})

const filteredTools = computed(() => {
  let filtered = tools.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(tool =>
      tool.name.toLowerCase().includes(query) ||
      tool.description.toLowerCase().includes(query) ||
      tool.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }

  // 分类过滤
  if (selectedCategory.value) {
    filtered = filtered.filter(tool => tool.category === selectedCategory.value)
  }

  // 难度过滤
  if (selectedDifficulty.value) {
    filtered = filtered.filter(tool => tool.difficulty === selectedDifficulty.value)
  }

  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'name':
        return a.name.localeCompare(b.name)
      case 'popularity':
        return (b.isFavorite ? 1 : 0) - (a.isFavorite ? 1 : 0)
      case 'date':
        return (b.isNew ? 1 : 0) - (a.isNew ? 1 : 0)
      default:
        return 0
    }
  })

  return filtered
})

// 方法
const clearFilters = () => {
  searchQuery.value = ''
  selectedCategory.value = ''
  selectedDifficulty.value = ''
}

const navigateToTool = (tool: any) => {
  router.push(tool.path)
}

const toggleFavorite = (tool: any) => {
  tool.isFavorite = !tool.isFavorite
  // 这里可以调用 API 保存收藏状态
}

// 生命周期
onMounted(() => {
  // 这里可以从 API 加载工具数据
})
</script>
