import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Notification, NotificationAction } from '@/types'

export const useNotificationStore = defineStore('notification', () => {
  // 状态
  const notifications = ref<Notification[]>([])
  const maxNotifications = ref(5)
  const defaultDuration = ref(5000) // 5秒

  // 计算属性
  const hasNotifications = computed(() => notifications.value.length > 0)
  const notificationCount = computed(() => notifications.value.length)

  // 生成唯一ID
  const generateId = (): string => {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 添加通知
  const addNotification = (notification: Omit<Notification, 'id' | 'createdAt'>): string => {
    const id = generateId()
    const newNotification: Notification = {
      id,
      createdAt: new Date(),
      duration: notification.duration ?? defaultDuration.value,
      ...notification
    }

    // 如果超过最大数量，移除最旧的通知
    if (notifications.value.length >= maxNotifications.value) {
      notifications.value.shift()
    }

    notifications.value.push(newNotification)

    // 自动移除通知
    if (newNotification.duration && newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id)
      }, newNotification.duration)
    }

    return id
  }

  // 移除通知
  const removeNotification = (id: string): void => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  // 清除所有通知
  const clearAll = (): void => {
    notifications.value = []
  }

  // 更新通知
  const updateNotification = (id: string, updates: Partial<Notification>): void => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      Object.assign(notification, updates)
    }
  }

  // 便捷方法：成功通知
  const success = (title: string, message?: string, options?: Partial<Notification>): string => {
    return addNotification({
      type: 'success',
      title,
      message,
      ...options
    })
  }

  // 便捷方法：错误通知
  const error = (title: string, message?: string, options?: Partial<Notification>): string => {
    return addNotification({
      type: 'error',
      title,
      message,
      duration: 0, // 错误通知默认不自动消失
      ...options
    })
  }

  // 便捷方法：警告通知
  const warning = (title: string, message?: string, options?: Partial<Notification>): string => {
    return addNotification({
      type: 'warning',
      title,
      message,
      duration: 8000, // 警告通知显示更长时间
      ...options
    })
  }

  // 便捷方法：信息通知
  const info = (title: string, message?: string, options?: Partial<Notification>): string => {
    return addNotification({
      type: 'info',
      title,
      message,
      ...options
    })
  }

  // 便捷方法：带操作的通知
  const withActions = (
    type: Notification['type'],
    title: string,
    message: string,
    actions: NotificationAction[],
    options?: Partial<Notification>
  ): string => {
    return addNotification({
      type,
      title,
      message,
      actions,
      duration: 0, // 带操作的通知不自动消失
      ...options
    })
  }

  // 便捷方法：确认通知
  const confirm = (
    title: string,
    message: string,
    onConfirm: () => void,
    onCancel?: () => void,
    options?: Partial<Notification>
  ): string => {
    const actions: NotificationAction[] = [
      {
        label: '确认',
        action: onConfirm,
        style: 'primary'
      }
    ]

    if (onCancel) {
      actions.push({
        label: '取消',
        action: onCancel,
        style: 'secondary'
      })
    }

    return withActions('info', title, message, actions, options)
  }

  // 便捷方法：复制成功通知
  const copySuccess = (content?: string): string => {
    return success(
      '复制成功',
      content ? `已复制: ${content.length > 50 ? content.substring(0, 50) + '...' : content}` : '内容已复制到剪贴板'
    )
  }

  // 便捷方法：操作成功通知
  const operationSuccess = (operation: string, target?: string): string => {
    return success(
      `${operation}成功`,
      target ? `${target} 已${operation}` : undefined
    )
  }

  // 便捷方法：操作失败通知
  const operationError = (operation: string, error?: string, target?: string): string => {
    return error(
      `${operation}失败`,
      error || (target ? `${target} ${operation}失败` : `${operation}时发生错误`)
    )
  }

  // 便捷方法：网络错误通知
  const networkError = (message?: string): string => {
    return error(
      '网络错误',
      message || '请检查您的网络连接后重试'
    )
  }

  // 便捷方法：权限错误通知
  const permissionError = (action?: string): string => {
    return error(
      '权限不足',
      action ? `您没有权限执行此操作: ${action}` : '您没有权限执行此操作'
    )
  }

  // 便捷方法：验证错误通知
  const validationError = (field: string, message?: string): string => {
    return error(
      '输入验证失败',
      message || `${field} 格式不正确`
    )
  }

  // 便捷方法：加载状态通知
  const loading = (message: string = '加载中...'): string => {
    return addNotification({
      type: 'info',
      title: message,
      duration: 0 // 加载通知需要手动移除
    })
  }

  // 设置配置
  const setMaxNotifications = (max: number): void => {
    maxNotifications.value = Math.max(1, max)
    
    // 如果当前通知数量超过新的最大值，移除多余的
    while (notifications.value.length > maxNotifications.value) {
      notifications.value.shift()
    }
  }

  const setDefaultDuration = (duration: number): void => {
    defaultDuration.value = Math.max(0, duration)
  }

  // 获取特定类型的通知
  const getNotificationsByType = (type: Notification['type']): Notification[] => {
    return notifications.value.filter(n => n.type === type)
  }

  // 检查是否存在特定类型的通知
  const hasNotificationType = (type: Notification['type']): boolean => {
    return notifications.value.some(n => n.type === type)
  }

  return {
    // 状态
    notifications,
    maxNotifications,
    defaultDuration,
    
    // 计算属性
    hasNotifications,
    notificationCount,
    
    // 基础方法
    addNotification,
    removeNotification,
    clearAll,
    updateNotification,
    
    // 便捷方法
    success,
    error,
    warning,
    info,
    withActions,
    confirm,
    copySuccess,
    operationSuccess,
    operationError,
    networkError,
    permissionError,
    validationError,
    loading,
    
    // 配置方法
    setMaxNotifications,
    setDefaultDuration,
    
    // 查询方法
    getNotificationsByType,
    hasNotificationType
  }
})
