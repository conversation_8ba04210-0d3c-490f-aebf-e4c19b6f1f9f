@import 'tailwindcss';

/* 基础样式重置 */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  tab-size: 4;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  font-variation-settings: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 通用工具类 */
.container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.section-padding {
  @apply py-12 lg:py-16;
}

.card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700;
}

.card-hover {
  @apply transition-all duration-200 hover:shadow-medium hover:-translate-y-1;
}

.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 dark:bg-primary-500 dark:hover:bg-primary-600;
}

.btn-secondary {
  @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600;
}

.btn-outline {
  @apply btn border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700;
}

.btn-ghost {
  @apply btn text-gray-600 hover:bg-gray-100 focus:ring-gray-500 dark:text-gray-400 dark:hover:bg-gray-700;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn-lg {
  @apply px-6 py-3 text-base;
}

.input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 dark:text-white dark:focus:ring-primary-400 dark:focus:border-primary-400;
}

.input-error {
  @apply border-red-300 focus:ring-red-500 focus:border-red-500 dark:border-red-600;
}

.label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
}

.error-text {
  @apply text-sm text-red-600 dark:text-red-400 mt-1;
}

.help-text {
  @apply text-sm text-gray-500 dark:text-gray-400 mt-1;
}

/* 动画类 */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

.animate-bounce-gentle {
  animation: bounceGentle 0.6s ease-out;
}

/* 响应式文本大小 */
.text-responsive-xs {
  @apply text-xs sm:text-sm;
}

.text-responsive-sm {
  @apply text-sm sm:text-base;
}

.text-responsive-base {
  @apply text-base sm:text-lg;
}

.text-responsive-lg {
  @apply text-lg sm:text-xl;
}

.text-responsive-xl {
  @apply text-xl sm:text-2xl;
}

.text-responsive-2xl {
  @apply text-2xl sm:text-3xl;
}

.text-responsive-3xl {
  @apply text-3xl sm:text-4xl;
}

/* 网格布局工具 */
.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

/* 特殊效果 */
.glass-effect {
  @apply bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/20 dark:border-gray-700/20;
}

.gradient-text {
  @apply bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent;
}

.gradient-bg {
  @apply bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20;
}

/* 代码块样式 */
.code-block {
  @apply bg-gray-100 dark:bg-gray-800 rounded-lg p-4 font-mono text-sm overflow-x-auto;
}

.inline-code {
  @apply bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm font-mono;
}

/* 加载状态 */
.loading-skeleton {
  @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
}

.loading-dots::after {
  content: '';
  animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
  
  .print-break-inside-avoid {
    page-break-inside: avoid;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .card {
    @apply border-2 border-gray-900 dark:border-gray-100;
  }
  
  .btn {
    @apply border-2;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
