{"name": "toolhub", "version": "1.0.0", "description": "ToolHub - 现代化在线工具集平台", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:e2e": "playwright test", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "@vueuse/core": "^10.5.0", "vue-i18n": "^9.8.0", "crypto-js": "^4.2.0", "jszip": "^3.10.0", "qrcode": "^1.5.0", "html2canvas": "^1.4.0", "monaco-editor": "^0.45.0", "dayjs": "^1.11.0", "pinyin-pro": "^3.18.0", "tesseract.js": "^5.0.0", "pdf-lib": "^1.17.0", "pdfjs-dist": "^3.11.0", "wavesurfer.js": "^7.0.0", "ffmpeg.wasm": "^0.12.0", "axios": "^1.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.4.0", "typescript": "^5.2.0", "vue-tsc": "^1.8.0", "vite": "^4.4.0", "tailwindcss": "^4.1.0", "@tailwindcss/vite": "^4.1.0", "vitest": "^0.34.0", "@vue/test-utils": "^2.4.0", "jsdom": "^22.1.0", "@playwright/test": "^1.40.0", "eslint": "^8.50.0", "@typescript-eslint/parser": "^6.7.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@vue/eslint-config-typescript": "^12.0.0", "prettier": "^3.0.0", "@vue/tsconfig": "^0.4.0"}, "keywords": ["vue", "vite", "tailwindcss", "tools", "online-tools", "utilities", "developer-tools"], "author": "ToolHub Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/toolhub/toolhub.git"}, "homepage": "https://toolhub.dev", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}