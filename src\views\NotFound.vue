<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
    <div class="text-center">
      <!-- 404 图标 -->
      <div class="mb-8">
        <Icon name="mdi:alert-circle-outline" class="h-24 w-24 text-gray-400 mx-auto" />
      </div>

      <!-- 错误信息 -->
      <h1 class="text-6xl font-bold text-gray-900 dark:text-white mb-4">404</h1>
      <h2 class="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4">
        {{ $t('error.404') }}
      </h2>
      <p class="text-gray-500 dark:text-gray-400 mb-8 max-w-md mx-auto">
        {{ $t('error.404Desc') }}
      </p>

      <!-- 操作按钮 -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <RouterLink to="/" class="btn btn-primary">
          <Icon name="mdi:home" class="h-5 w-5 mr-2" />
          {{ $t('error.goHome') }}
        </RouterLink>
        <button class="btn btn-outline" @click="goBack">
          <Icon name="mdi:arrow-left" class="h-5 w-5 mr-2" />
          {{ $t('common.back') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import Icon from '@/components/common/Icon.vue'

const router = useRouter()
const { t } = useI18n()

const goBack = () => {
  if (window.history.length > 1) {
    router.back()
  } else {
    router.push('/')
  }
}
</script>
