<template>
  <div id="app" :class="{ 'dark': isDark }">
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      <!-- 全局加载指示器 -->
      <div v-if="isLoading" class="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900">
        <div class="flex flex-col items-center space-y-4">
          <div class="w-12 h-12 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin"></div>
          <p class="text-gray-600 dark:text-gray-400">{{ $t('common.loading') }}</p>
        </div>
      </div>

      <!-- 主要内容 -->
      <div v-else>
        <!-- 导航栏 -->
        <AppHeader />
        
        <!-- 主要内容区域 -->
        <main class="flex-1">
          <RouterView v-slot="{ Component, route }">
            <Transition
              :name="route.meta?.transition || 'fade'"
              mode="out-in"
              appear
            >
              <component :is="Component" :key="route.path" />
            </Transition>
          </RouterView>
        </main>

        <!-- 页脚 -->
        <AppFooter />
      </div>

      <!-- 全局通知 -->
      <GlobalNotifications />
      
      <!-- 全局模态框 -->
      <GlobalModals />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, provide } from 'vue'
import { RouterView } from 'vue-router'
import { useDark } from '@vueuse/core'
import { useI18n } from 'vue-i18n'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppFooter from '@/components/layout/AppFooter.vue'
import GlobalNotifications from '@/components/common/GlobalNotifications.vue'
import GlobalModals from '@/components/common/GlobalModals.vue'

// 响应式数据
const isLoading = ref(true)
const isDark = useDark()
const { t } = useI18n()

// 提供全局状态
provide('isDark', isDark)

// 生命周期
onMounted(async () => {
  try {
    // 模拟初始化过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 这里可以进行一些初始化操作
    // 比如检查用户登录状态、加载用户偏好设置等
    
    isLoading.value = false
  } catch (error) {
    console.error('App initialization failed:', error)
    isLoading.value = false
  }
})
</script>

<style>
/* 全局样式 */
#app {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 路由过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s ease;
}

.slide-left-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.slide-left-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.5);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.7);
}

/* 选择文本样式 */
::selection {
  background-color: rgba(59, 130, 246, 0.3);
}

.dark ::selection {
  background-color: rgba(96, 165, 250, 0.3);
}
</style>
