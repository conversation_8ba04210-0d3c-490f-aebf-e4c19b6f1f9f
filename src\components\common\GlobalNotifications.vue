<template>
  <Teleport to="body">
    <div class="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
      <TransitionGroup
        name="notification"
        tag="div"
        class="space-y-2"
      >
        <div
          v-for="notification in notifications"
          :key="notification.id"
          :class="[
            'relative overflow-hidden rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 backdrop-blur-sm',
            notificationClasses[notification.type]
          ]"
        >
          <!-- 进度条 -->
          <div
            v-if="notification.duration && notification.duration > 0"
            class="absolute top-0 left-0 h-1 bg-current opacity-30 transition-all ease-linear"
            :style="{ width: `${getProgress(notification)}%` }"
          ></div>

          <div class="p-4">
            <div class="flex items-start">
              <!-- 图标 -->
              <div class="flex-shrink-0">
                <Icon
                  :name="notificationIcons[notification.type]"
                  :class="iconClasses[notification.type]"
                  class="h-5 w-5"
                />
              </div>

              <!-- 内容 -->
              <div class="ml-3 flex-1">
                <p class="text-sm font-medium" :class="titleClasses[notification.type]">
                  {{ notification.title }}
                </p>
                <p
                  v-if="notification.message"
                  class="mt-1 text-sm"
                  :class="messageClasses[notification.type]"
                >
                  {{ notification.message }}
                </p>

                <!-- 操作按钮 -->
                <div v-if="notification.actions && notification.actions.length > 0" class="mt-3 flex space-x-2">
                  <button
                    v-for="action in notification.actions"
                    :key="action.label"
                    :class="[
                      'text-xs font-medium rounded-md px-2 py-1 transition-colors',
                      action.style === 'primary' ? actionPrimaryClasses[notification.type] : actionSecondaryClasses[notification.type]
                    ]"
                    @click="handleAction(notification, action)"
                  >
                    {{ action.label }}
                  </button>
                </div>
              </div>

              <!-- 关闭按钮 -->
              <div class="ml-4 flex-shrink-0">
                <button
                  class="inline-flex rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2"
                  :class="closeButtonClasses[notification.type]"
                  @click="removeNotification(notification.id)"
                >
                  <Icon name="mdi:close" class="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import { useNotificationStore } from '@/stores/notification'
import type { Notification, NotificationAction } from '@/types'
import Icon from './Icon.vue'

// Store
const notificationStore = useNotificationStore()

// 计算属性
const notifications = computed(() => notificationStore.notifications)

// 样式配置
const notificationClasses = {
  success: 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800',
  error: 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800',
  warning: 'bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800',
  info: 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
}

const notificationIcons = {
  success: 'mdi:check-circle',
  error: 'mdi:close-circle',
  warning: 'mdi:alert',
  info: 'mdi:information-outline'
}

const iconClasses = {
  success: 'text-green-400',
  error: 'text-red-400',
  warning: 'text-yellow-400',
  info: 'text-blue-400'
}

const titleClasses = {
  success: 'text-green-800 dark:text-green-200',
  error: 'text-red-800 dark:text-red-200',
  warning: 'text-yellow-800 dark:text-yellow-200',
  info: 'text-blue-800 dark:text-blue-200'
}

const messageClasses = {
  success: 'text-green-700 dark:text-green-300',
  error: 'text-red-700 dark:text-red-300',
  warning: 'text-yellow-700 dark:text-yellow-300',
  info: 'text-blue-700 dark:text-blue-300'
}

const actionPrimaryClasses = {
  success: 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-800 dark:text-green-200 dark:hover:bg-green-700',
  error: 'bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-800 dark:text-red-200 dark:hover:bg-red-700',
  warning: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200 dark:bg-yellow-800 dark:text-yellow-200 dark:hover:bg-yellow-700',
  info: 'bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-800 dark:text-blue-200 dark:hover:bg-blue-700'
}

const actionSecondaryClasses = {
  success: 'text-green-800 hover:bg-green-100 dark:text-green-200 dark:hover:bg-green-800',
  error: 'text-red-800 hover:bg-red-100 dark:text-red-200 dark:hover:bg-red-800',
  warning: 'text-yellow-800 hover:bg-yellow-100 dark:text-yellow-200 dark:hover:bg-yellow-800',
  info: 'text-blue-800 hover:bg-blue-100 dark:text-blue-200 dark:hover:bg-blue-800'
}

const closeButtonClasses = {
  success: 'text-green-400 hover:text-green-500 focus:ring-green-500',
  error: 'text-red-400 hover:text-red-500 focus:ring-red-500',
  warning: 'text-yellow-400 hover:text-yellow-500 focus:ring-yellow-500',
  info: 'text-blue-400 hover:text-blue-500 focus:ring-blue-500'
}

// 方法
const removeNotification = (id: string) => {
  notificationStore.removeNotification(id)
}

const handleAction = (notification: Notification, action: NotificationAction) => {
  action.action()
  removeNotification(notification.id)
}

const getProgress = (notification: Notification): number => {
  if (!notification.duration || notification.duration <= 0) return 100
  
  const elapsed = Date.now() - notification.createdAt.getTime()
  const progress = Math.max(0, 100 - (elapsed / notification.duration) * 100)
  return progress
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    notificationStore.clearAll()
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
/* 通知动画 */
.notification-enter-active {
  transition: all 0.3s ease-out;
}

.notification-leave-active {
  transition: all 0.3s ease-in;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}
</style>
