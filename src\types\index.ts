// 基础类型定义
export interface BaseEntity {
  id: string
  createdAt: Date
  updatedAt: Date
}

// 工具相关类型
export interface Tool extends BaseEntity {
  name: string
  description: string
  category: ToolCategory
  icon: string
  path: string
  tags: string[]
  isPopular: boolean
  isNew: boolean
  difficulty: 'easy' | 'medium' | 'hard'
  estimatedTime: number // 预估使用时间（分钟）
  features: string[]
  limitations?: string[]
  relatedTools?: string[]
  metadata?: Record<string, any>
}

// 工具分类
export interface ToolCategory {
  id: string
  name: string
  description: string
  icon: string
  color: string
  parentId?: string
  children?: ToolCategory[]
  toolCount: number
}

// 用户相关类型
export interface User {
  id: string
  username: string
  email: string
  avatar?: string
  preferences: UserPreferences
  favoriteTools: string[]
  recentTools: string[]
  createdAt: Date
  lastLoginAt: Date
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto'
  language: string
  timezone: string
  notifications: NotificationSettings
  privacy: PrivacySettings
}

export interface NotificationSettings {
  email: boolean
  browser: boolean
  updates: boolean
  tips: boolean
}

export interface PrivacySettings {
  analytics: boolean
  cookies: boolean
  dataCollection: boolean
}

// 应用状态类型
export interface AppState {
  isLoading: boolean
  error: string | null
  user: User | null
  currentTool: Tool | null
  searchQuery: string
  selectedCategory: string | null
  filters: ToolFilters
}

export interface ToolFilters {
  category?: string
  tags?: string[]
  difficulty?: string[]
  isPopular?: boolean
  isNew?: boolean
  searchQuery?: string
}

// 通知系统类型
export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number
  actions?: NotificationAction[]
  createdAt: Date
}

export interface NotificationAction {
  label: string
  action: () => void
  style?: 'primary' | 'secondary'
}

// 模态框类型
export interface Modal {
  id: string
  component: string
  props?: Record<string, any>
  options?: ModalOptions
}

export interface ModalOptions {
  closable?: boolean
  maskClosable?: boolean
  width?: string | number
  height?: string | number
  zIndex?: number
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  code?: number
}

export interface PaginatedResponse<T = any> {
  data: T[]
  total: number
  page: number
  pageSize: number
  hasNext: boolean
  hasPrev: boolean
}

// 文件处理类型
export interface FileInfo {
  name: string
  size: number
  type: string
  lastModified: number
  content?: string | ArrayBuffer
}

export interface ProcessingResult {
  success: boolean
  data?: any
  error?: string
  warnings?: string[]
  metadata?: Record<string, any>
}

// 工具配置类型
export interface ToolConfig {
  id: string
  name: string
  version: string
  settings: Record<string, any>
  shortcuts?: KeyboardShortcut[]
  customizations?: Record<string, any>
}

export interface KeyboardShortcut {
  key: string
  modifiers: string[]
  action: string
  description: string
}

// 主题类型
export interface Theme {
  id: string
  name: string
  colors: ThemeColors
  fonts: ThemeFonts
  spacing: ThemeSpacing
  borderRadius: ThemeBorderRadius
  shadows: ThemeShadows
}

export interface ThemeColors {
  primary: string
  secondary: string
  accent: string
  background: string
  surface: string
  text: string
  textSecondary: string
  border: string
  error: string
  warning: string
  success: string
  info: string
}

export interface ThemeFonts {
  sans: string[]
  mono: string[]
  sizes: Record<string, string>
  weights: Record<string, number>
}

export interface ThemeSpacing {
  xs: string
  sm: string
  md: string
  lg: string
  xl: string
}

export interface ThemeBorderRadius {
  sm: string
  md: string
  lg: string
  xl: string
  full: string
}

export interface ThemeShadows {
  sm: string
  md: string
  lg: string
  xl: string
}

// 国际化类型
export interface LocaleMessage {
  [key: string]: string | LocaleMessage
}

export interface SupportedLocale {
  code: string
  name: string
  nativeName: string
  flag: string
  rtl: boolean
}

// 搜索类型
export interface SearchResult {
  tool: Tool
  score: number
  highlights: string[]
}

export interface SearchFilters {
  categories?: string[]
  tags?: string[]
  difficulty?: string[]
  features?: string[]
}

// 分析统计类型
export interface ToolUsageStats {
  toolId: string
  views: number
  uses: number
  favorites: number
  shares: number
  rating: number
  reviews: number
}

export interface UserActivity {
  userId: string
  action: string
  toolId?: string
  metadata?: Record<string, any>
  timestamp: Date
}

// 错误类型
export interface AppError {
  code: string
  message: string
  details?: any
  stack?: string
  timestamp: Date
}

// 导出所有类型
export * from './api'
export * from './components'
export * from './router'
