// API 请求和响应类型定义

// 基础 API 类型
export interface ApiConfig {
  baseURL: string
  timeout: number
  headers: Record<string, string>
  retries: number
  retryDelay: number
}

export interface ApiRequest {
  url: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  params?: Record<string, any>
  data?: any
  headers?: Record<string, string>
  timeout?: number
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  code?: number
  timestamp: string
}

export interface ApiError {
  code: string
  message: string
  details?: any
  status: number
  timestamp: string
}

// 分页相关类型
export interface PaginationParams {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginatedResponse<T = any> {
  data: T[]
  pagination: {
    total: number
    page: number
    pageSize: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// 工具 API 类型
export interface ToolsApiParams extends PaginationParams {
  category?: string
  tags?: string[]
  search?: string
  difficulty?: string[]
  isPopular?: boolean
  isNew?: boolean
}

export interface ToolApiResponse {
  id: string
  name: string
  description: string
  category: string
  icon: string
  path: string
  tags: string[]
  isPopular: boolean
  isNew: boolean
  difficulty: 'easy' | 'medium' | 'hard'
  estimatedTime: number
  features: string[]
  limitations?: string[]
  relatedTools?: string[]
  stats: {
    views: number
    uses: number
    favorites: number
    rating: number
  }
  createdAt: string
  updatedAt: string
}

// 分类 API 类型
export interface CategoryApiResponse {
  id: string
  name: string
  description: string
  icon: string
  color: string
  parentId?: string
  children?: CategoryApiResponse[]
  toolCount: number
  createdAt: string
  updatedAt: string
}

// 用户 API 类型
export interface UserApiResponse {
  id: string
  username: string
  email: string
  avatar?: string
  preferences: {
    theme: 'light' | 'dark' | 'auto'
    language: string
    timezone: string
    notifications: {
      email: boolean
      browser: boolean
      updates: boolean
      tips: boolean
    }
    privacy: {
      analytics: boolean
      cookies: boolean
      dataCollection: boolean
    }
  }
  favoriteTools: string[]
  recentTools: string[]
  stats: {
    toolsUsed: number
    timeSpent: number
    favoriteCount: number
  }
  createdAt: string
  lastLoginAt: string
}

export interface UpdateUserRequest {
  username?: string
  email?: string
  avatar?: string
  preferences?: Partial<UserApiResponse['preferences']>
}

// 认证 API 类型
export interface LoginRequest {
  email: string
  password: string
  rememberMe?: boolean
}

export interface LoginResponse {
  user: UserApiResponse
  token: string
  refreshToken: string
  expiresIn: number
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  confirmPassword: string
  acceptTerms: boolean
}

export interface RefreshTokenRequest {
  refreshToken: string
}

export interface ResetPasswordRequest {
  email: string
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

// 搜索 API 类型
export interface SearchRequest {
  query: string
  filters?: {
    categories?: string[]
    tags?: string[]
    difficulty?: string[]
    features?: string[]
  }
  pagination?: PaginationParams
}

export interface SearchResponse {
  results: Array<{
    tool: ToolApiResponse
    score: number
    highlights: string[]
  }>
  suggestions: string[]
  filters: {
    categories: Array<{ id: string; name: string; count: number }>
    tags: Array<{ name: string; count: number }>
    difficulty: Array<{ level: string; count: number }>
  }
  pagination: PaginatedResponse<any>['pagination']
}

// 统计 API 类型
export interface ToolStatsRequest {
  toolId: string
  action: 'view' | 'use' | 'favorite' | 'share'
  metadata?: Record<string, any>
}

export interface ToolStatsResponse {
  toolId: string
  stats: {
    views: number
    uses: number
    favorites: number
    shares: number
    rating: number
    reviews: number
  }
  trends: {
    daily: Array<{ date: string; count: number }>
    weekly: Array<{ week: string; count: number }>
    monthly: Array<{ month: string; count: number }>
  }
}

export interface UserStatsResponse {
  userId: string
  stats: {
    toolsUsed: number
    timeSpent: number
    favoriteCount: number
    sessionsCount: number
  }
  activity: Array<{
    date: string
    toolsUsed: number
    timeSpent: number
  }>
  topTools: Array<{
    toolId: string
    name: string
    usageCount: number
    timeSpent: number
  }>
}

// 反馈 API 类型
export interface FeedbackRequest {
  type: 'bug' | 'feature' | 'improvement' | 'other'
  title: string
  description: string
  toolId?: string
  priority: 'low' | 'medium' | 'high'
  attachments?: string[]
  userAgent?: string
  url?: string
}

export interface FeedbackResponse {
  id: string
  status: 'pending' | 'in-progress' | 'resolved' | 'closed'
  createdAt: string
  updatedAt: string
}

// 文件上传 API 类型
export interface FileUploadRequest {
  file: File
  purpose: 'avatar' | 'attachment' | 'tool-data'
  metadata?: Record<string, any>
}

export interface FileUploadResponse {
  id: string
  filename: string
  originalName: string
  size: number
  mimeType: string
  url: string
  thumbnailUrl?: string
  metadata?: Record<string, any>
  createdAt: string
}

// 导出配置 API 类型
export interface ExportRequest {
  type: 'tools' | 'favorites' | 'history' | 'settings'
  format: 'json' | 'csv' | 'xml'
  filters?: Record<string, any>
}

export interface ExportResponse {
  id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  downloadUrl?: string
  expiresAt?: string
  createdAt: string
}

// 导入配置 API 类型
export interface ImportRequest {
  type: 'tools' | 'favorites' | 'settings'
  file: File
  options?: {
    overwrite?: boolean
    merge?: boolean
    validate?: boolean
  }
}

export interface ImportResponse {
  id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  results?: {
    imported: number
    skipped: number
    errors: number
  }
  errors?: Array<{
    line: number
    message: string
    data?: any
  }>
  createdAt: string
}
