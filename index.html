<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="ToolHub - 现代化在线工具集平台，为开发者、设计师和内容创作者提供丰富实用的在线工具" />
    <meta name="keywords" content="在线工具,开发工具,编码工具,转换工具,图片工具,PDF工具,文本工具" />
    <meta name="author" content="ToolHub Team" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://toolhub.dev/" />
    <meta property="og:title" content="ToolHub - 现代化在线工具集平台" />
    <meta property="og:description" content="为开发者、设计师和内容创作者提供丰富实用的在线工具" />
    <meta property="og:image" content="/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://toolhub.dev/" />
    <meta property="twitter:title" content="ToolHub - 现代化在线工具集平台" />
    <meta property="twitter:description" content="为开发者、设计师和内容创作者提供丰富实用的在线工具" />
    <meta property="twitter:image" content="/og-image.png" />

    <!-- Theme Color -->
    <meta name="theme-color" content="#3b82f6" />
    <meta name="msapplication-TileColor" content="#3b82f6" />
    
    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet" />
    
    <title>ToolHub - 现代化在线工具集平台</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
