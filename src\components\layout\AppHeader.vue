<template>
  <header class="sticky top-0 z-40 w-full border-b border-gray-200 dark:border-gray-700 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex h-16 items-center justify-between">
        <!-- Logo 和品牌 -->
        <div class="flex items-center space-x-4">
          <RouterLink to="/" class="flex items-center space-x-2 group">
            <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform">
              <span class="text-white font-bold text-sm">TH</span>
            </div>
            <span class="text-xl font-bold text-gray-900 dark:text-white hidden sm:block">
              ToolHub
            </span>
          </RouterLink>
        </div>

        <!-- 导航菜单 -->
        <nav class="hidden md:flex items-center space-x-8">
          <RouterLink
            v-for="item in navigationItems"
            :key="item.path"
            :to="item.path"
            class="text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white transition-colors font-medium"
            active-class="text-primary-600 dark:text-primary-400"
          >
            {{ $t(item.label) }}
          </RouterLink>
        </nav>

        <!-- 搜索框 -->
        <div class="flex-1 max-w-lg mx-8 hidden lg:block">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon name="mdi:magnify" class="h-5 w-5 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              :placeholder="$t('home.searchPlaceholder')"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-800 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 text-sm"
              @keyup.enter="handleSearch"
            />
          </div>
        </div>

        <!-- 右侧操作区 -->
        <div class="flex items-center space-x-4">
          <!-- 搜索按钮（移动端） -->
          <button
            class="lg:hidden p-2 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
            @click="showMobileSearch = !showMobileSearch"
          >
            <Icon name="mdi:magnify" class="h-5 w-5" />
          </button>

          <!-- 主题切换 -->
          <button
            class="p-2 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 transition-colors"
            @click="toggleTheme"
            :title="$t('settings.theme')"
          >
            <Icon :name="isDark ? 'mdi:weather-sunny' : 'mdi:weather-night'" class="h-5 w-5" />
          </button>

          <!-- 语言切换 -->
          <div class="relative" ref="languageDropdown">
            <button
              class="p-2 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 transition-colors"
              @click="showLanguageMenu = !showLanguageMenu"
              :title="$t('settings.language')"
            >
              <Icon name="mdi:translate" class="h-5 w-5" />
            </button>
            
            <Transition
              enter-active-class="transition ease-out duration-100"
              enter-from-class="transform opacity-0 scale-95"
              enter-to-class="transform opacity-100 scale-100"
              leave-active-class="transition ease-in duration-75"
              leave-from-class="transform opacity-100 scale-100"
              leave-to-class="transform opacity-0 scale-95"
            >
              <div
                v-if="showLanguageMenu"
                class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
              >
                <div class="py-1">
                  <button
                    v-for="locale in supportedLocales"
                    :key="locale.code"
                    class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    :class="{ 'bg-gray-100 dark:bg-gray-700': currentLocale === locale.code }"
                    @click="switchLanguage(locale.code)"
                  >
                    <span class="mr-2">{{ locale.flag }}</span>
                    {{ locale.nativeName }}
                  </button>
                </div>
              </div>
            </Transition>
          </div>

          <!-- 移动端菜单按钮 -->
          <button
            class="md:hidden p-2 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
            @click="showMobileMenu = !showMobileMenu"
          >
            <Icon :name="showMobileMenu ? 'mdi:close' : 'mdi:menu'" class="h-6 w-6" />
          </button>
        </div>
      </div>

      <!-- 移动端搜索框 -->
      <Transition
        enter-active-class="transition ease-out duration-200"
        enter-from-class="opacity-0 -translate-y-1"
        enter-to-class="opacity-100 translate-y-0"
        leave-active-class="transition ease-in duration-150"
        leave-from-class="opacity-100 translate-y-0"
        leave-to-class="opacity-0 -translate-y-1"
      >
        <div v-if="showMobileSearch" class="lg:hidden py-4 border-t border-gray-200 dark:border-gray-700">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon name="mdi:magnify" class="h-5 w-5 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              :placeholder="$t('home.searchPlaceholder')"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-800 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              @keyup.enter="handleSearch"
            />
          </div>
        </div>
      </Transition>

      <!-- 移动端导航菜单 -->
      <Transition
        enter-active-class="transition ease-out duration-200"
        enter-from-class="opacity-0 -translate-y-1"
        enter-to-class="opacity-100 translate-y-0"
        leave-active-class="transition ease-in duration-150"
        leave-from-class="opacity-100 translate-y-0"
        leave-to-class="opacity-0 -translate-y-1"
      >
        <div v-if="showMobileMenu" class="md:hidden py-4 border-t border-gray-200 dark:border-gray-700">
          <nav class="flex flex-col space-y-2">
            <RouterLink
              v-for="item in navigationItems"
              :key="item.path"
              :to="item.path"
              class="px-3 py-2 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white transition-colors font-medium rounded-md hover:bg-gray-100 dark:hover:bg-gray-800"
              active-class="text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20"
              @click="showMobileMenu = false"
            >
              {{ $t(item.label) }}
            </RouterLink>
          </nav>
        </div>
      </Transition>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useDark, useToggle, onClickOutside } from '@vueuse/core'
import { supportedLocales, switchLanguage as switchLang } from '@/locales'
import Icon from '@/components/common/Icon.vue'

// 响应式数据
const searchQuery = ref('')
const showMobileMenu = ref(false)
const showMobileSearch = ref(false)
const showLanguageMenu = ref(false)
const languageDropdown = ref()

// 组合式 API
const router = useRouter()
const { locale } = useI18n()
const isDark = useDark()
const toggleTheme = useToggle(isDark)

// 计算属性
const currentLocale = computed(() => locale.value)

// 导航菜单项
const navigationItems = [
  { path: '/', label: 'nav.home' },
  { path: '/tools', label: 'nav.tools' },
  { path: '/categories', label: 'nav.categories' },
  { path: '/favorites', label: 'nav.favorites' },
  { path: '/history', label: 'nav.history' }
]

// 方法
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({
      name: 'Search',
      query: { q: searchQuery.value.trim() }
    })
    showMobileSearch.value = false
  }
}

const switchLanguage = (localeCode: string) => {
  switchLang(localeCode as any, { global: { locale } } as any)
  showLanguageMenu.value = false
}

// 点击外部关闭语言菜单
onClickOutside(languageDropdown, () => {
  showLanguageMenu.value = false
})

// 监听路由变化，关闭移动端菜单
router.afterEach(() => {
  showMobileMenu.value = false
  showMobileSearch.value = false
})

// 监听 ESC 键关闭菜单
const handleEscape = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    showMobileMenu.value = false
    showMobileSearch.value = false
    showLanguageMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleEscape)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleEscape)
})
</script>
