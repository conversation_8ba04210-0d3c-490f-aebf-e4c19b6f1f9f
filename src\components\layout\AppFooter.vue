<template>
  <footer class="bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- 品牌信息 -->
        <div class="col-span-1 lg:col-span-2">
          <div class="flex items-center space-x-2 mb-4">
            <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-sm">TH</span>
            </div>
            <span class="text-xl font-bold text-gray-900 dark:text-white">ToolHub</span>
          </div>
          <p class="text-gray-600 dark:text-gray-400 mb-4 max-w-md">
            {{ $t('about.description') }}
          </p>
          <div class="flex space-x-4">
            <a
              v-for="social in socialLinks"
              :key="social.name"
              :href="social.url"
              :title="social.name"
              class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 transition-colors"
              target="_blank"
              rel="noopener noreferrer"
            >
              <Icon :name="social.icon" class="h-5 w-5" />
            </a>
          </div>
        </div>

        <!-- 工具分类 -->
        <div>
          <h3 class="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4">
            {{ $t('nav.tools') }}
          </h3>
          <ul class="space-y-2">
            <li v-for="category in popularCategories" :key="category.key">
              <RouterLink
                :to="`/tools?category=${category.key}`"
                class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white transition-colors"
              >
                {{ $t(`categories.${category.key}`) }}
              </RouterLink>
            </li>
          </ul>
        </div>

        <!-- 快速链接 -->
        <div>
          <h3 class="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4">
            {{ $t('common.quickAccess') }}
          </h3>
          <ul class="space-y-2">
            <li v-for="link in quickLinks" :key="link.path">
              <RouterLink
                :to="link.path"
                class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white transition-colors"
              >
                {{ $t(link.label) }}
              </RouterLink>
            </li>
          </ul>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="border-t border-gray-200 dark:border-gray-700 mt-8 pt-8">
        <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <!-- 版权信息 -->
          <div class="text-gray-500 dark:text-gray-400 text-sm">
            <p>
              © {{ currentYear }} ToolHub. {{ $t('common.allRightsReserved') }}
            </p>
          </div>

          <!-- 法律链接 -->
          <div class="flex space-x-6 text-sm">
            <RouterLink
              to="/privacy"
              class="text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white transition-colors"
            >
              {{ $t('common.privacy') }}
            </RouterLink>
            <RouterLink
              to="/terms"
              class="text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white transition-colors"
            >
              {{ $t('common.terms') }}
            </RouterLink>
            <RouterLink
              to="/contact"
              class="text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white transition-colors"
            >
              {{ $t('common.contact') }}
            </RouterLink>
          </div>

          <!-- 版本信息 -->
          <div class="text-gray-500 dark:text-gray-400 text-sm">
            <span>{{ $t('common.version') }} {{ appVersion }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 回到顶部按钮 -->
    <Transition
      enter-active-class="transition ease-out duration-300"
      enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100"
      leave-active-class="transition ease-in duration-200"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-95"
    >
      <button
        v-if="showBackToTop"
        class="fixed bottom-8 right-8 p-3 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg transition-colors z-30"
        @click="scrollToTop"
        :title="$t('common.backToTop')"
      >
        <Icon name="mdi:chevron-up" class="h-5 w-5" />
      </button>
    </Transition>
  </footer>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import Icon from '@/components/common/Icon.vue'

// 响应式数据
const showBackToTop = ref(false)

// 组合式 API
const { t } = useI18n()

// 计算属性
const currentYear = computed(() => new Date().getFullYear())
const appVersion = computed(() => '1.0.0') // 这里可以从 package.json 获取

// 社交媒体链接
const socialLinks = [
  {
    name: 'GitHub',
    icon: 'mdi:github',
    url: 'https://github.com/toolhub/toolhub'
  },
  {
    name: 'Twitter',
    icon: 'mdi:twitter',
    url: 'https://twitter.com/toolhub'
  },
  {
    name: 'Discord',
    icon: 'mdi:discord',
    url: 'https://discord.gg/toolhub'
  },
  {
    name: 'Email',
    icon: 'mdi:email',
    url: 'mailto:<EMAIL>'
  }
]

// 热门分类
const popularCategories = [
  { key: 'encoding' },
  { key: 'json' },
  { key: 'text' },
  { key: 'image' },
  { key: 'qr' },
  { key: 'hash' }
]

// 快速链接
const quickLinks = [
  { path: '/favorites', label: 'nav.favorites' },
  { path: '/history', label: 'nav.history' },
  { path: '/settings', label: 'nav.settings' },
  { path: '/about', label: 'nav.about' }
]

// 方法
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
}

// 生命周期
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>
