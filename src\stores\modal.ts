import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Modal, ModalOptions } from '@/types'

export const useModalStore = defineStore('modal', () => {
  // 状态
  const modals = ref<Modal[]>([])
  const maxModals = ref(5)

  // 计算属性
  const hasModals = computed(() => modals.value.length > 0)
  const modalCount = computed(() => modals.value.length)
  const topModal = computed(() => modals.value[modals.value.length - 1] || null)

  // 生成唯一ID
  const generateId = (): string => {
    return `modal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 打开模态框
  const openModal = (
    component: string,
    props?: Record<string, any>,
    options?: ModalOptions
  ): string => {
    const id = generateId()
    
    // 如果超过最大数量，关闭最旧的模态框
    if (modals.value.length >= maxModals.value) {
      modals.value.shift()
    }

    const modal: Modal = {
      id,
      component,
      props: props || {},
      options: {
        closable: true,
        maskClosable: true,
        keyboard: true,
        zIndex: 1000,
        ...options
      }
    }

    modals.value.push(modal)
    return id
  }

  // 关闭模态框
  const closeModal = (id: string): void => {
    const index = modals.value.findIndex(m => m.id === id)
    if (index > -1) {
      modals.value.splice(index, 1)
    }
  }

  // 关闭所有模态框
  const closeAll = (): void => {
    modals.value = []
  }

  // 关闭顶层模态框
  const closeTop = (): void => {
    if (modals.value.length > 0) {
      modals.value.pop()
    }
  }

  // 更新模态框
  const updateModal = (id: string, updates: Partial<Modal>): void => {
    const modal = modals.value.find(m => m.id === id)
    if (modal) {
      Object.assign(modal, updates)
    }
  }

  // 更新模态框属性
  const updateModalProps = (id: string, props: Record<string, any>): void => {
    const modal = modals.value.find(m => m.id === id)
    if (modal) {
      modal.props = { ...modal.props, ...props }
    }
  }

  // 更新模态框选项
  const updateModalOptions = (id: string, options: Partial<ModalOptions>): void => {
    const modal = modals.value.find(m => m.id === id)
    if (modal) {
      modal.options = { ...modal.options, ...options }
    }
  }

  // 便捷方法：确认对话框
  const confirm = (
    title: string,
    message: string,
    onConfirm?: () => void,
    onCancel?: () => void,
    options?: ModalOptions
  ): string => {
    return openModal('ConfirmDialog', {
      title,
      message,
      onConfirm,
      onCancel
    }, {
      width: '400px',
      ...options
    })
  }

  // 便捷方法：警告对话框
  const alert = (
    title: string,
    message: string,
    onOk?: () => void,
    options?: ModalOptions
  ): string => {
    return openModal('AlertDialog', {
      title,
      message,
      onOk
    }, {
      width: '400px',
      ...options
    })
  }

  // 便捷方法：输入对话框
  const prompt = (
    title: string,
    message: string,
    defaultValue?: string,
    onConfirm?: (value: string) => void,
    onCancel?: () => void,
    options?: ModalOptions
  ): string => {
    return openModal('PromptDialog', {
      title,
      message,
      defaultValue,
      onConfirm,
      onCancel
    }, {
      width: '400px',
      ...options
    })
  }

  // 便捷方法：图片预览
  const previewImage = (
    src: string,
    alt?: string,
    options?: ModalOptions
  ): string => {
    return openModal('ImagePreview', {
      src,
      alt
    }, {
      width: '80vw',
      height: '80vh',
      maskClosable: true,
      ...options
    })
  }

  // 便捷方法：设置对话框
  const openSettings = (
    activeTab?: string,
    options?: ModalOptions
  ): string => {
    return openModal('SettingsDialog', {
      activeTab
    }, {
      width: '600px',
      height: '500px',
      ...options
    })
  }

  // 便捷方法：关于对话框
  const openAbout = (options?: ModalOptions): string => {
    return openModal('AboutDialog', {}, {
      width: '500px',
      ...options
    })
  }

  // 便捷方法：反馈对话框
  const openFeedback = (
    toolId?: string,
    options?: ModalOptions
  ): string => {
    return openModal('FeedbackDialog', {
      toolId
    }, {
      width: '500px',
      ...options
    })
  }

  // 便捷方法：删除确认
  const confirmDelete = (
    itemName: string,
    onConfirm: () => void,
    onCancel?: () => void,
    options?: ModalOptions
  ): string => {
    return confirm(
      '确认删除',
      `确定要删除 "${itemName}" 吗？此操作不可撤销。`,
      onConfirm,
      onCancel,
      options
    )
  }

  // 便捷方法：清空确认
  const confirmClear = (
    itemType: string,
    onConfirm: () => void,
    onCancel?: () => void,
    options?: ModalOptions
  ): string => {
    return confirm(
      '确认清空',
      `确定要清空所有${itemType}吗？此操作不可撤销。`,
      onConfirm,
      onCancel,
      options
    )
  }

  // 便捷方法：保存确认
  const confirmSave = (
    message: string = '确定要保存当前更改吗？',
    onConfirm: () => void,
    onCancel?: () => void,
    options?: ModalOptions
  ): string => {
    return confirm(
      '保存更改',
      message,
      onConfirm,
      onCancel,
      options
    )
  }

  // 便捷方法：离开确认
  const confirmLeave = (
    message: string = '您有未保存的更改，确定要离开吗？',
    onConfirm: () => void,
    onCancel?: () => void,
    options?: ModalOptions
  ): string => {
    return confirm(
      '确认离开',
      message,
      onConfirm,
      onCancel,
      options
    )
  }

  // 便捷方法：错误提示
  const showError = (
    title: string,
    message: string,
    onOk?: () => void,
    options?: ModalOptions
  ): string => {
    return alert(title, message, onOk, {
      type: 'error',
      ...options
    })
  }

  // 便捷方法：成功提示
  const showSuccess = (
    title: string,
    message: string,
    onOk?: () => void,
    options?: ModalOptions
  ): string => {
    return alert(title, message, onOk, {
      type: 'success',
      ...options
    })
  }

  // 便捷方法：警告提示
  const showWarning = (
    title: string,
    message: string,
    onOk?: () => void,
    options?: ModalOptions
  ): string => {
    return alert(title, message, onOk, {
      type: 'warning',
      ...options
    })
  }

  // 检查模态框是否存在
  const hasModal = (id: string): boolean => {
    return modals.value.some(m => m.id === id)
  }

  // 获取模态框
  const getModal = (id: string): Modal | undefined => {
    return modals.value.find(m => m.id === id)
  }

  // 设置最大模态框数量
  const setMaxModals = (max: number): void => {
    maxModals.value = Math.max(1, max)
    
    // 如果当前模态框数量超过新的最大值，关闭多余的
    while (modals.value.length > maxModals.value) {
      modals.value.shift()
    }
  }

  return {
    // 状态
    modals,
    maxModals,
    
    // 计算属性
    hasModals,
    modalCount,
    topModal,
    
    // 基础方法
    openModal,
    closeModal,
    closeAll,
    closeTop,
    updateModal,
    updateModalProps,
    updateModalOptions,
    
    // 便捷方法
    confirm,
    alert,
    prompt,
    previewImage,
    openSettings,
    openAbout,
    openFeedback,
    confirmDelete,
    confirmClear,
    confirmSave,
    confirmLeave,
    showError,
    showSuccess,
    showWarning,
    
    // 查询方法
    hasModal,
    getModal,
    
    // 配置方法
    setMaxModals
  }
})
