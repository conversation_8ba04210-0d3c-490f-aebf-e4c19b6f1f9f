<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          {{ $t('categories.title') }}
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mb-8">
          {{ $t('categories.subtitle') }}
        </p>
        <div class="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
          <Icon name="mdi:construction" class="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
          <span class="text-blue-800 dark:text-blue-200">{{ $t('common.comingSoon') }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import Icon from '@/components/common/Icon.vue'

const { t } = useI18n()
</script>
